package com.entity.sla;

import java.util.List;
import com.dto.sla.SlaMetricThresholdDTO;
import com.enums.sla.SlaWorkingTimeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.*;

@Setter
@Getter
@Data
@Entity
@Table(name = "sla_template_metrics")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlaTemplateMetric {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "sla_template_id", nullable = false)
    private Long slaTemplateId;

    @Column(name = "sla_template_code", nullable = false)
    private String slaTemplateCode;

    @Column(name = "metric_id", nullable = false)
    private Long metricId;

    @Column(name = "metric_name", nullable = false)
    private String metricName;

    @Type(type = "jsonb")
    @Column(name = "target_threshold", columnDefinition = "jsonb")
    private List<SlaMetricThresholdDTO> targetThreshold;

    @Type(type = "jsonb")
    @Column(name = "warning_threshold", columnDefinition = "jsonb")
    private List<SlaMetricThresholdDTO> warningThreshold;

    @Type(type = "jsonb")
    @Column(name = "breach_threshold", columnDefinition = "jsonb")
    private List<SlaMetricThresholdDTO> breachThreshold;

    @Column(name = "working_time")
    @Enumerated(EnumType.STRING)
    private SlaWorkingTimeEnum workingTime; // OFFICE_HOURS, SUPPORT_247
}




