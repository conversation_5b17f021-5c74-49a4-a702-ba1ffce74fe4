package com.service.sla.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.dto.common.ICommonIdName;
import com.dto.sla.SlaTemplateMetricConfigDTO;
import com.dto.sla.request.SlaTemplateAlarmCreateDTO;
import com.dto.sla.request.SlaTemplateCreateDTO;
import com.dto.sla.request.SlaTemplateDeleteReqDTO;
import com.dto.sla.request.SlaTemplateUpdateDTO;
import com.dto.sla.response.SlaTemplateDeleteResDTO;
import com.dto.sla.response.SlaTemplateResponseDTO;
import com.entity.sla.SlaMetric;
import com.entity.sla.SlaTemplate;
import com.entity.sla.SlaTemplateAlarm;
import com.entity.sla.SlaTemplateMetric;
import com.enums.sla.SlaSourceTypeEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.Status;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.repository.sla.SlaMetricRepository;
import com.repository.sla.SlaPolicyRepository;
import com.repository.sla.SlaTemplateAlarmRepository;
import com.repository.sla.SlaTemplateMetricRepository;
import com.repository.sla.SlaTemplateRepository;
import com.service.sla.SlaTemplateService;
import com.util.AuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class SlaTemplateServiceImpl implements SlaTemplateService {

    private final SlaTemplateRepository slaTemplateRepository;
    private final SlaPolicyRepository slaPolicyRepository;
    private final ExceptionFactory exceptionFactory;
    private final SlaTemplateMetricRepository slaTemplateMetricRepository;
    private final SlaTemplateAlarmRepository slaTemplateAlarmRepository;
    private final SlaMetricRepository slaMetricRepository;


    @Override
    public void deleteSlaTemplate(Long slaId) {
        SlaTemplate slaTemplate = slaTemplateRepository.findById(slaId)
                .orElseThrow(() -> new ResourceNotFoundException(MessageKeyConstant.NOT_FOUND, Resources.SLA_TEMPLATE, ErrorKey.ID,MessageKeyConstant.NOT_FOUND));
        boolean existsTemplate = slaPolicyRepository.existsBySlaTemplateId(slaId);
        // check trạng thái tempalte
        if(slaTemplate.getStatus() == Status.ACTIVE.value){
            throw exceptionFactory.badRequest(MessageKeyConstant.Sla.STATUS_SLA_TEMPLATE_ACTIVE, Resources.SLA_TEMPLATE, ErrorKey.STATUS);
        }
        // check template có đang được chính sách sử dụng
        if(existsTemplate){
            throw exceptionFactory.badRequest(MessageKeyConstant.Sla.USED_SLA_TEMPLATE_POLICY, Resources.SLA_TEMPLATE, ErrorKey.ID);
        }
        slaTemplate.setDeletedFlag(DeletedFlag.DELETED.getValue());
        slaTemplateRepository.save(slaTemplate);
    }

    /**
     * Lấy danh sách sla template để tạo chính sách
     */
    @Override
    public List<ICommonIdName> getSlaTemplates(String name) {
        return slaTemplateRepository.getSlaTemplates(name);
    }

    /**
     * Tạo mới SLA Template
     */
    @Override
    @Transactional
    public SlaTemplateResponseDTO createSlaTemplate(SlaTemplateCreateDTO request) {
        // Validate tên không trùng lặp
        Long currentUserId = AuthUtil.getCurrentUserId();

        // Tạo SLA Template
        SlaTemplate template = SlaTemplate.builder()
            .name(request.getName())
            .code(generateTemplateCode())
            .description(request.getDescription())
            .type(request.getType())
            .status(request.getStatus() == StatusEnum.ACTIVE ? 1 : 0)
            .sourceType(SlaSourceTypeEnum.CUSTOM)
            .deletedFlag(DeletedFlag.NOT_YET_DELETED.getValue())
            .build();

        template.setCreatedBy(currentUserId);
        template.setModifiedBy(currentUserId);

        SlaTemplate savedTemplate = slaTemplateRepository.save(template);

        // Lưu metrics và alarms nếu có
        if (!CollectionUtils.isEmpty(request.getMetrics())) {
            saveTemplateMetricsWithAlarms(savedTemplate.getId(), savedTemplate.getCode(), request.getMetrics());
        }

        log.info("Tạo mới SLA Template thành công với ID: {}", savedTemplate.getId());
        return getSlaTemplateById(savedTemplate.getId());
    }

    /**
     * Lưu template metrics và alarms
     */
    private void saveTemplateMetricsWithAlarms(Long templateId, String templateCode, List<SlaTemplateMetricConfigDTO> metricConfigs) {
        for (SlaTemplateMetricConfigDTO config : metricConfigs) {
            // Lấy thông tin metric để lấy tên
            SlaMetric metric = slaMetricRepository.findById(config.getMetricId())
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SLA_METRIC, ErrorKey.ID, config.getMetricId()));

            // Tạo và lưu metric
            SlaTemplateMetric templateMetric = SlaTemplateMetric.builder()
                .slaTemplateId(templateId)
                .slaTemplateCode(templateCode)
                .metricId(config.getMetricId())
                .metricName(metric.getLabel())
                .targetThreshold(config.getTargetThreshold())
                .warningThreshold(config.getWarningThreshold())
                .breachThreshold(config.getBreachThreshold())
                .workingTime(config.getWorkingTime())
                .build();

            SlaTemplateMetric savedMetric = slaTemplateMetricRepository.save(templateMetric);

            // Lưu alarms cho metric này
            if (!CollectionUtils.isEmpty(config.getAlarms())) {
                saveAlarmsForMetric(savedMetric.getId(), config.getAlarms());
            }
        }
    }

    /**
     * Lưu alarms cho một metric
     */
    private void saveAlarmsForMetric(Long metricId, List<SlaTemplateAlarmCreateDTO> alarmDTOs) {
        List<SlaTemplateAlarm> alarms = alarmDTOs.stream()
            .map(dto -> SlaTemplateAlarm.builder()
                .name(dto.getName())
                .slaTemplateMetricId(metricId) // Sử dụng ID của metric đã lưu
                .thresholdType(dto.getThresholdType())
                .action(dto.getAction())
                .notificationId(dto.getNotificationId())
                .attributeId(dto.getAttributeId())
                .attributeValue(dto.getAttributeValue())
                .assigneeId(dto.getAssigneeId())
                .build())
            .collect(Collectors.toList());

        slaTemplateAlarmRepository.saveAll(alarms);
    }
    /**
     * Cập nhật SLA Template
     */
    @Override
    @Transactional
    public SlaTemplateResponseDTO updateSlaTemplate(SlaTemplateUpdateDTO request) {
        log.info("Cập nhật SLA Template với ID: {}", request.getId());

        // Tìm template hiện tại
        SlaTemplate existingTemplate = slaTemplateRepository.findByIdAndNotDeleted(request.getId())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SLA_TEMPLATE, ErrorKey.ID, request.getId()));

        Long currentUserId = AuthUtil.getCurrentUserId();
        // Cập nhật thông tin template
        existingTemplate.setName(request.getName());
        existingTemplate.setDescription(request.getDescription());
        existingTemplate.setType(request.getType());
        existingTemplate.setStatus(request.getStatus() == StatusEnum.ACTIVE ? 1 : 0);
        existingTemplate.setModifiedBy(currentUserId);
        existingTemplate.setModifiedAt(new Date());

        SlaTemplate savedTemplate = slaTemplateRepository.save(existingTemplate);

        // Xóa metrics và alarms cũ
        deleteTemplateMetricsAndAlarms(savedTemplate.getId());

        // Lưu metrics và alarms mới nếu có
        if (!CollectionUtils.isEmpty(request.getMetrics())) {
            saveTemplateMetricsWithAlarms(savedTemplate.getId(), savedTemplate.getCode(), request.getMetrics());
        }

        log.info("Cập nhật SLA Template thành công với ID: {}", savedTemplate.getId());
        return getSlaTemplateById(savedTemplate.getId());
    }

    /**
     * Lấy thông tin chi tiết SLA Template theo ID
     */
    public SlaTemplateResponseDTO getSlaTemplateById(Long id) {
        log.info("Lấy thông tin SLA Template với ID: {}", id);

        SlaTemplate template = slaTemplateRepository.findByIdAndNotDeleted(id)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SLA_TEMPLATE, ErrorKey.ID, id));

        return convertToResponseDTO(template);
    }

    /**
     * Xóa nhiều SLA Template (soft delete)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SlaTemplateDeleteResDTO deleteSlaTemplates(SlaTemplateDeleteReqDTO request) {
        log.info("Bắt đầu xóa nhiều SLA Template với {} IDs", request.getIds().size());

        Set<Long> ids = request.getIds();
        Long currentUserId = AuthUtil.getCurrentUserId();

        // Kiểm tra các template tồn tại và chưa bị xóa
        List<SlaTemplate> existingTemplates = slaTemplateRepository.findByIdInAndDeletedFlag(
            new ArrayList<>(ids), DeletedFlag.NOT_YET_DELETED.getValue());

        // Kiểm tra tất cả IDs có tồn tại không
        if (existingTemplates.size() != ids.size()) {
            Set<Long> foundIds = existingTemplates.stream()
                .map(SlaTemplate::getId)
                .collect(Collectors.toSet());
            Set<Long> notFoundIds = new HashSet<>(ids);
            notFoundIds.removeAll(foundIds);
            throw exceptionFactory.badRequest("Không tìm thấy SLA Template với IDs: " + notFoundIds,
                Resources.SLA_TEMPLATE, ErrorKey.SlaMetric.ID);
        }
        // Thực hiện soft delete tất cả
        Date now = new Date();
        for (SlaTemplate template : existingTemplates) {
            template.setDeletedFlag(DeletedFlag.DELETED.getValue());
            template.setModifiedBy(currentUserId);
            template.setModifiedAt(now);
        }

        slaTemplateRepository.saveAll(existingTemplates);

        // Tạo response
        SlaTemplateDeleteResDTO response = new SlaTemplateDeleteResDTO();
        response.setCode("SUCCESS");
        response.setMessage("Xóa SLA Template thành công");
        log.info("Xóa nhiều SLA Template hoàn thành - Đã xóa: {} templates", existingTemplates.size());
        return response;
    }


    /**
     * Lấy danh sách SLA Template active để chọn khi tạo policy
     */
    @Override
    public List<SlaTemplateResponseDTO> getActiveTemplates() {
        log.info("Lấy danh sách SLA Template active");

        List<SlaTemplate> templates = slaTemplateRepository.findAllActiveTemplates();

        return templates.stream()
            .map(this::convertToResponseDTO)
            .collect(Collectors.toList());
    }

    /**
     * Kiểm tra tên SLA Template có trùng lặp không
     */
    @Override
    public boolean isNameExists(String name, Long excludeId) {
        return slaTemplateRepository.findByNameAndNotDeleted(name, excludeId).isPresent();
    }

    /**
     * Generate mã SLA Template
     */
    @Override
    public String generateTemplateCode() {
        // Tạo mã theo format: SLT + timestamp
        return "SLT" + System.currentTimeMillis();
    }

    // ==================== HELPER METHODS ====================

    /**
     * Xóa template metrics và alarms
     */
    private void deleteTemplateMetricsAndAlarms(Long templateId) {
        List<SlaTemplateMetric> metrics = slaTemplateMetricRepository.findBySlaTemplateId(templateId);
        if (!CollectionUtils.isEmpty(metrics)) {
            List<Long> metricIds = metrics.stream()
                .map(SlaTemplateMetric::getId)
                .collect(Collectors.toList());

            // Xóa alarms trước
            slaTemplateAlarmRepository.deleteBySlaTemplateMetricIdIn(metricIds);

            // Xóa metrics
            slaTemplateMetricRepository.deleteBySlaTemplateId(templateId);
        }
    }

    /**
     * Sao chép template metrics
     */
    private void cloneTemplateMetrics(Long newTemplateId, String newTemplateCode, List<SlaTemplateMetric> originalMetrics) {
        List<SlaTemplateMetric> newMetrics = originalMetrics.stream()
            .map(original -> SlaTemplateMetric.builder()
                .slaTemplateId(newTemplateId)
                .slaTemplateCode(newTemplateCode)
                .metricId(original.getMetricId())
                .metricName(original.getMetricName()) // Sao chép metric name từ original
                .targetThreshold(original.getTargetThreshold())
                .warningThreshold(original.getWarningThreshold())
                .breachThreshold(original.getBreachThreshold())
                .workingTime(original.getWorkingTime())
                .build())
            .collect(Collectors.toList());

        List<SlaTemplateMetric> savedMetrics = slaTemplateMetricRepository.saveAll(newMetrics);

        // Sao chép alarms cho từng metric
        for (int i = 0; i < originalMetrics.size(); i++) {
            SlaTemplateMetric originalMetric = originalMetrics.get(i);
            SlaTemplateMetric newMetric = savedMetrics.get(i);

            List<SlaTemplateAlarm> originalAlarms = slaTemplateAlarmRepository.findBySlaTemplateMetricId(originalMetric.getId());
            if (!CollectionUtils.isEmpty(originalAlarms)) {
                cloneTemplateAlarms(newMetric.getId(), originalAlarms);
            }
        }
    }

    /**
     * Sao chép template alarms
     */
    private void cloneTemplateAlarms(Long newMetricId, List<SlaTemplateAlarm> originalAlarms) {
        List<SlaTemplateAlarm> newAlarms = originalAlarms.stream()
            .map(original -> SlaTemplateAlarm.builder()
                .name(original.getName())
                .slaTemplateMetricId(newMetricId)
                .thresholdType(original.getThresholdType())
                .action(original.getAction())
                .notificationId(original.getNotificationId())
                .attributeId(original.getAttributeId())
                .attributeValue(original.getAttributeValue())
                .assigneeId(original.getAssigneeId())
                .build())
            .collect(Collectors.toList());

        slaTemplateAlarmRepository.saveAll(newAlarms);
    }

    /**
     * Convert entity sang response DTO
     */
    private SlaTemplateResponseDTO convertToResponseDTO(SlaTemplate template) {
        // Lấy metrics
        List<SlaTemplateMetric> metrics = slaTemplateMetricRepository.findBySlaTemplateId(template.getId());
        List<SlaTemplateResponseDTO.SlaTemplateMetricResponseDTO> metricDTOs = new ArrayList<>();
        List<SlaTemplateResponseDTO.SlaTemplateAlarmResponseDTO> alarmDTOs = new ArrayList<>();

        for (SlaTemplateMetric metric : metrics) {
            // Convert metric
            SlaTemplateResponseDTO.SlaTemplateMetricResponseDTO metricDTO =
                SlaTemplateResponseDTO.SlaTemplateMetricResponseDTO.builder()
                    .id(metric.getId())
                    .metricId(metric.getMetricId())
                    .metricName("") // TODO: Lấy từ SlaMetric table
                    .metricLabel("") // TODO: Lấy từ SlaMetric table
                    .targetThreshold(convertThresholdListToResponse(metric.getTargetThreshold()))
                    .warningThreshold(convertThresholdListToResponse(metric.getWarningThreshold()))
                    .breachThreshold(convertThresholdListToResponse(metric.getBreachThreshold()))
                    .workingTime(metric.getWorkingTime() != null ? metric.getWorkingTime().name() : null)
                    .build();
            metricDTOs.add(metricDTO);

            // Lấy alarms cho metric này
            List<SlaTemplateAlarm> alarms = slaTemplateAlarmRepository.findBySlaTemplateMetricId(metric.getId());
            for (SlaTemplateAlarm alarm : alarms) {
                SlaTemplateResponseDTO.SlaTemplateAlarmResponseDTO alarmDTO =
                    SlaTemplateResponseDTO.SlaTemplateAlarmResponseDTO.builder()
                        .id(alarm.getId())
                        .name(alarm.getName())
                        .slaTemplateMetricId(alarm.getSlaTemplateMetricId())
                        .thresholdType(alarm.getThresholdType() != null ? alarm.getThresholdType().name() : null)
                        .action(alarm.getAction() != null ? alarm.getAction().name() : null)
                        .notificationId(alarm.getNotificationId())
                        .attributeId(alarm.getAttributeId())
                        .attributeValue(alarm.getAttributeValue())
                        .assigneeId(alarm.getAssigneeId())
                        .build();
                alarmDTOs.add(alarmDTO);
            }
        }

        return SlaTemplateResponseDTO.builder()
            .id(template.getId())
            .name(template.getName())
            .code(template.getCode())
            .description(template.getDescription())
            .type(template.getType())
            .status(template.getStatusEnum())
            .createdBy(template.getCreatedBy())
            .createdAt(template.getCreatedAt())
            .modifiedBy(template.getModifiedBy())
            .modifiedAt(template.getModifiedAt())
            .metrics(metricDTOs)
            .alarms(alarmDTOs)
            .build();
    }

    /**
     * Convert threshold DTO sang response DTO
     */
    private SlaTemplateResponseDTO.SlaMetricThresholdResponseDTO convertThresholdToResponse(
        com.dto.sla.SlaMetricThresholdDTO threshold) {
        if (threshold == null) {
            return null;
        }

        return SlaTemplateResponseDTO.SlaMetricThresholdResponseDTO.builder()
            .operator(threshold.getOperator() != null ? threshold.getOperator().name() : null)
            .value(threshold.getValue())
            .unit(threshold.getUnit())
            .build();
    }

    /**
     * Convert danh sách threshold DTO sang response DTO
     */
    private List<SlaTemplateResponseDTO.SlaMetricThresholdResponseDTO> convertThresholdListToResponse(
        List<com.dto.sla.SlaMetricThresholdDTO> thresholds) {
        if (thresholds == null || thresholds.isEmpty()) {
            return null;
        }

        return thresholds.stream()
            .map(this::convertThresholdToResponse)
            .collect(Collectors.toList());
    }
}
