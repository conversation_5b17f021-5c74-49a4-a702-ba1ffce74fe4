package com.service.sla.impl;

import com.dto.sla.IGetListPolicies;
import com.entity.sla.SlaPolicy;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.Status;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.utils.SqlUtils;
import com.repository.sla.SlaPolicyRepository;
import com.dto.sla.SlaPolicyCreateDTO;
import com.service.sla.SlaPolicyService;
import com.service.utils.condition.ConditionExecute;
import com.util.AuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SlaPolicyServiceImpl implements SlaPolicyService {

    private final SlaPolicyRepository slaPolicyRepository;
    private final ExceptionFactory exceptionFactory;

    @Override
    public Page<IGetListPolicies> getListPolicies(Integer isPoliciesName,String value, List<Status> status,
                                                  Long createdBy, Date startDate, Date endDate, Pageable pageable) {

        value = SqlUtils.optimizeSearchLike(value);
        List<Integer> statusList = status.stream().map(s -> s.value ).collect(Collectors.toList());

        return slaPolicyRepository.getListPolicies(isPoliciesName, value, statusList, createdBy, startDate, endDate, pageable);
    }

    @Override
    public void deletePolicies(Long policiesId) {
        SlaPolicy slaPolicy = slaPolicyRepository.findById(policiesId)
                .orElseThrow(() -> new ResourceNotFoundException(MessageKeyConstant.NOT_FOUND, Resources.SLA_POLICIES, ErrorKey.ID,MessageKeyConstant.NOT_FOUND));
        // check trạng thái của chính sách
        if(slaPolicy.getStatus() == Status.ACTIVE.value){
            throw exceptionFactory.badRequest(MessageKeyConstant.Sla.STATUS_POLICY_ACTIVE, Resources.SLA_POLICIES, ErrorKey.STATUS);
        }
        slaPolicy.setDeletedFlag(DeletedFlag.DELETED.getValue());
        slaPolicyRepository.save(slaPolicy);
    }

    /**
     * Tạo chính sách
     */
    @Override
    @Transactional
    public Long createSlaPolicy(SlaPolicyCreateDTO dto) {
        validateName(dto.getName(), -1L);

        SlaPolicy slaPolicy = new SlaPolicy();
        Long currentId = AuthUtil.getCurrentUserId();
        slaPolicy.setCreatedBy(currentId);
        slaPolicy.setModifiedBy(currentId);
        slaPolicy.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
        BeanUtils.copyProperties(dto, slaPolicy);

        // set apply condition sql
        setApplyConditionSql(dto, slaPolicy);
        SlaPolicy save = slaPolicyRepository.save(slaPolicy);
        return save.getId();
    }

    /**
     * Set sql phục vụ quét
     */
    private static void setApplyConditionSql(SlaPolicyCreateDTO dto, SlaPolicy slaPolicy) {
        if (dto.getApplyCond() != null) {
            ConditionExecute mcConditionExecute = new ConditionExecute(dto.getApplyCond());
            slaPolicy.setApplyCondSql(mcConditionExecute.getCondition(null));
        } else {
            slaPolicy.setApplyCondSql("false");
        }
    }

    /**
     * Chỉnh sửa chính sách
     */
    @Override
    @Transactional
    public Long updateSlaPolicy(SlaPolicyCreateDTO dto, Long id) {
        validateName(dto.getName(), id);

        SlaPolicy slaPolicy = getSlaById(id);
        Long currentId = AuthUtil.getCurrentUserId();
        BeanUtils.copyProperties(dto, slaPolicy);
        slaPolicy.setModifiedBy(currentId);

        // set apply condition sql
        setApplyConditionSql(dto, slaPolicy);
        SlaPolicy save = slaPolicyRepository.save(slaPolicy);
        return save.getId();
    }

    private SlaPolicy getSlaById(Long id) {
        return slaPolicyRepository.findById(id).orElseThrow( () -> exceptionFactory.badRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.SLA_POLICY, ErrorKey.ID));
    }

    /**
     * check trung ten metric group
     */
    @Override
    public void validateName(String name, Long id) {
        if(slaPolicyRepository.existsByNameAndDeletedFlagAndIdNot(name, DeletedFlag.NOT_YET_DELETED.getValue(), id)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.Sla.METRIC_GROUP_NAME_EXIST, ErrorKey.SLA_METRIC_GROUP,
                    ErrorKey.SlaMetric.NAME);
        }
    }
}
