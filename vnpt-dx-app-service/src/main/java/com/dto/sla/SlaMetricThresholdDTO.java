package com.dto.sla;

import com.service.utils.constants.OperatorConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlaMetricThresholdDTO {

    @NotNull(message = "Toán tử không được để trống")
    private OperatorConstant.OperatorEnum operator;

    @NotNull(message = "<PERSON>i<PERSON> trị không được để trống")
    private Double value;

    private String unit;

}
