package com.entity.sla;

import com.component.BaseEntity;
import com.service.utils.jsonObject.McConditionItemGroupDTO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

@Setter
@Getter
@Data
@Entity
@Table(name = "sla_policies")
public class SlaPolicy extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;

    private String description;

    @Column(name = "sla_template_id")
    private Long slaTemplateId;

    @Type(type = "jsonb")
    @Column(name = "apply_cond", columnDefinition = "jsonb")
    private List<McConditionItemGroupDTO> applyCond;

    @Column(name = "apply_cond_sql", columnDefinition = "text")
    private String applyCondSql;

    @Column
    private Short priority;

}
