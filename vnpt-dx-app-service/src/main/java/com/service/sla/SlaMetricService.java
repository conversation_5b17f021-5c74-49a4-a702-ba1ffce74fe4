package com.service.sla;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dto.common.ICommonIdName;
import com.dto.sla.IGetListMetric;
import com.enums.sla.SlaMetricTypeEnum;
import com.enums.sla.SlaObjectTypeEnum;

import java.util.Date;

import com.dto.sla.IGetSlaMetricGroupDTO;
import com.dto.sla.SlaMetricGroupCreateDTO;
import com.dto.sla.request.SlaMetricCreateRequestDTO;
import com.dto.sla.request.SlaMetricDeleteReqDTO;
import com.dto.sla.request.SlaMetricUpdateRequestDTO;
import com.dto.sla.request.SlaMetricValidateRequestDTO;
import com.dto.sla.response.SlaMetricDeleteResDTO;
import com.dto.sla.response.SlaMetricNameResponseDTO;
import com.dto.sla.response.SlaMetricResponseDTO;
import com.onedx.common.constants.enums.StatusEnum;

/**
 * Service interface cho quản lý SLA Metric
 */
public interface SlaMetricService {
    Long createMetricGroup(SlaMetricGroupCreateDTO request);

    Long updateMetricGroup(SlaMetricGroupCreateDTO request);

    Page<IGetSlaMetricGroupDTO> getMetricGroup(Integer isName, String value, StatusEnum status, Long createdBy, Pageable pageable);

    void deleteMetricGroup(Long id);

    /**
     * Tạo mới SLA Metric
     * @param request DTO chứa thông tin tạo mới
     * @return SLA Metric đã tạo
     */
    SlaMetricResponseDTO createSlaMetric(SlaMetricCreateRequestDTO request);

    /**
     * Cập nhật SLA Metric
     * @param request DTO chứa thông tin cập nhật
     * @return SLA Metric đã cập nhật
     */
    SlaMetricResponseDTO updateSlaMetric(SlaMetricUpdateRequestDTO request);

    /**
     * Xóa SLA Metric
     * @param id ID của SLA Metric cần xóa
     */
    void deleteSlaMetric(Long id);

    /**
     * Xóa nhiều SLA Metric
     * @param request DTO chứa danh sách ID cần xóa
     * @return Kết quả xóa
     */
    SlaMetricDeleteResDTO deleteSlaMetrics(SlaMetricDeleteReqDTO request);

    /**
     * Validate label và generate code cho SLA Metric
     * @param request DTO chứa label cần validate
     * @return Code và system name được generate
     */
    SlaMetricNameResponseDTO validateAndGenerateName(SlaMetricValidateRequestDTO request);

    /**
     * Lấy danh sách chỉ số theo nguồn dữ liệu
     */
    Page<IGetListMetric> getListMetric(Integer isMetricName, Integer isMetricCode, String value,
                                       List<Long> metricGroup, List<SlaObjectTypeEnum> dataSource, List<SlaMetricTypeEnum> slaMetricType,
                                       Date startDate, Date endDate, Pageable pageable);
    /**
     * Lấy danh sách droplist nhóm chỉ số màn danh sách chỉ số
     */
    List<ICommonIdName> getListMetricGroup();

    /**
     * Lấy danh sách droplist người tạo theo nhóm chỉ số
     */
    List<ICommonIdName> getCreatedByMetricGroup(String name);

    /**
     * Validate tên nhóm chỉ số
     */
    void validateNameMetricGroup(String name, Long id);
}
