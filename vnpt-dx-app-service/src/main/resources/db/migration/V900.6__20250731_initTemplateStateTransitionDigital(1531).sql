BEGIN;

-- ON VNPT---------------------------------------------------------------------------------------------------------------------------------------
-- Tạo temporary table để lưu trữ state_transition_id
CREATE TEMP TABLE IF NOT EXISTS temp_transition_id AS
SELECT id FROM vnpt_dev.state_transitions
WHERE name = 'Chuyển đổi trạng thái hàng hóa kỹ thuật số' AND object_type = 'DIGITAL';

-- Tạo temporary table để lưu trữ state_ids
CREATE TEMP TABLE IF NOT EXISTS temp_state_ids AS
SELECT id, name FROM vnpt_dev.states
WHERE name IN ('vnpt_installing', 'vnpt_installed', 'vnpt_crash');

-- X<PERSON><PERSON> dữ liệu cũ nếu có
DELETE FROM vnpt_dev.state_transition_item_triggers
WHERE state_transition_item_id IN (
    SELECT id FROM vnpt_dev.state_transition_items
    WHERE state_transition_id = (SELECT id FROM temp_transition_id)
      AND state_id IN (SELECT id FROM temp_state_ids)
);

DELETE FROM vnpt_dev.state_transition_items
WHERE state_transition_id = (SELECT id FROM temp_transition_id)
  AND state_id IN (SELECT id FROM temp_state_ids);

-- Insert dữ liệu cho trạng thái vnpt_installing
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id LIMIT 1;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_installing';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_installing';
END IF;

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           NULL,
           '[{
               "api": null,
               "type": "RULE_ENGINE",
               "manual": null,
               "webhook": null,
               "schedule": null,
               "ruleEngine": {
                 "conditions": [
                   {
                     "id": 1,
                     "key": "1",
                     "ifconds": [
                       {
                         "id": 1,
                         "key": "1",
                         "data": {
                           "value": ["VNPT"]
                         },
                         "operator": 1,
                         "operandId": 1059
                       }, {
                         "id": 1,
                         "key": "1",
                         "data": {
                           "value": ["0"]
                         },
                         "operator": 1,
                         "operandId": 6003
                       }
                     ]
                   }
                 ]
               }
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;


-- Insert dữ liệu cho trạng thái vnpt_installed
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_installed';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_installed';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'vnpt_installing';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_installing';
END IF;

    -- Lấy schedule_id
SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object thay vì nối chuỗi
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', null,
            'type', 'SCHEDULE',
            'manual', null,
            'webhook', null,
            'schedule', jsonb_build_object(
                'conditions', jsonb_build_array(
                    jsonb_build_object(
                        'id', 1,
                        'key', '1',
                        'ifconds', jsonb_build_array(
                            jsonb_build_object(
                                'id', 1,
                                'key', '1',
                                'data', jsonb_build_object('value', jsonb_build_array('INSTALLED')),
                                'operator', 1,
                                'operandId', 1063
                            )
                        )
                    )
                ),
                'scheduleId', v_schedule_id
            ),
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái vnpt_crash
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_crash';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_crash';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'vnpt_installed';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_installed';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', null,
            'type', 'SCHEDULE',
            'manual', null,
            'webhook', null,
            'schedule', jsonb_build_object(
                'conditions', jsonb_build_array(
                    jsonb_build_object(
                        'id', 1,
                        'key', '1',
                        'ifconds', jsonb_build_array(
                            jsonb_build_object(
                                'id', 1,
                                'key', '1',
                                'data', jsonb_build_object(
                                    'value', jsonb_build_array('ERROR')
                                ),
                                'operator', 1,
                                'operandId', 1063
                            )
                        )
                    )
                ),
                'scheduleId', v_schedule_id
            ),
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;


-- Xóa temporary tables sau khi sử dụng
DROP TABLE IF EXISTS temp_transition_id;
DROP TABLE IF EXISTS temp_state_ids;


-- ON PARTNER------------------------------------------------------------------------------------------------------------------------------------
-- Tạo temporary table để lưu trữ state_transition_id
CREATE TEMP TABLE IF NOT EXISTS temp_transition_id AS
SELECT id FROM vnpt_dev.state_transitions
WHERE name = 'Chuyển đổi trạng thái hàng hóa kỹ thuật số' AND object_type = 'DIGITAL';

-- Tạo temporary table để lưu trữ state_ids
CREATE TEMP TABLE IF NOT EXISTS temp_state_ids AS
SELECT id, name FROM vnpt_dev.states
WHERE name IN ('partner_installing', 'partner_installed', 'partner_crash');

-- Xóa dữ liệu cũ nếu có
DELETE FROM vnpt_dev.state_transition_item_triggers
WHERE state_transition_item_id IN (
    SELECT id FROM vnpt_dev.state_transition_items
    WHERE state_transition_id = (SELECT id FROM temp_transition_id)
      AND state_id IN (SELECT id FROM temp_state_ids)
);

DELETE FROM vnpt_dev.state_transition_items
WHERE state_transition_id = (SELECT id FROM temp_transition_id)
  AND state_id IN (SELECT id FROM temp_state_ids);

-- Insert dữ liệu cho trạng thái partner_installing
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'partner_installing';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           NULL,
           '[{
             "api": null,
             "type": "RULE_ENGINE",
             "manual": null,
             "webhook": null,
             "schedule": null,
             "ruleEngine": {
               "conditions": [
                 {
                   "id": 1,
                   "key": "1",
                   "ifconds": [
                     {
                       "id": 1,
                       "key": "1",
                       "data": {
                         "value": ["VNPT_PARTNER"]
                       },
                       "operator": 1,
                       "operandId": 1059
                     }, {
                       "id": 1,
                       "key": "1",
                       "data": {
                         "value": ["0"]
                       },
                       "operator": 1,
                       "operandId": 6003
                     }
                   ]
                 }
               ]
             }
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái partner_installed
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'partner_installed';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên partner_installed';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'partner_installing';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên partner_installing';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', null,
            'type', 'SCHEDULE',
            'manual', null,
            'webhook', null,
            'schedule', jsonb_build_object(
                'conditions', jsonb_build_array(
                    jsonb_build_object(
                        'id', 1,
                        'key', '1',
                        'ifconds', jsonb_build_array(
                            jsonb_build_object(
                                'id', 1,
                                'key', '1',
                                'data', jsonb_build_object('value', jsonb_build_array('INSTALLED')),
                                'operator', 1,
                                'operandId', 1063
                            )
                        )
                    )
                ),
                'scheduleId', v_schedule_id
            ),
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;


-- Insert dữ liệu cho trạng thái partner_crash
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'partner_crash';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên partner_crash';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'partner_installed';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên partner_installed';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', null,
            'type', 'SCHEDULE',
            'manual', null,
            'webhook', null,
            'schedule', jsonb_build_object(
                'conditions', jsonb_build_array(
                    jsonb_build_object(
                        'id', 1,
                        'key', '1',
                        'ifconds', jsonb_build_array(
                            jsonb_build_object(
                                'id', 1,
                                'key', '1',
                                'data', jsonb_build_object('value', jsonb_build_array('ERROR')),
                                'operator', 1,
                                'operandId', 1063
                            )
                        )
                    )
                ),
                'scheduleId', v_schedule_id
            ),
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;


-- Xóa temporary tables sau khi sử dụng
DROP TABLE IF EXISTS temp_transition_id;
DROP TABLE IF EXISTS temp_state_ids;

--OS VNPT----------------------------------------------------------------------------------------------------------------------------------------
-- Tạo temporary table để lưu trữ state_transition_id
CREATE TEMP TABLE IF NOT EXISTS temp_transition_id AS
SELECT id FROM vnpt_dev.state_transitions
WHERE name = 'Chuyển đổi trạng thái hàng hóa kỹ thuật số' AND object_type = 'DIGITAL';

-- Tạo temporary table để lưu trữ state_ids
CREATE TEMP TABLE IF NOT EXISTS temp_state_ids AS
SELECT id, name FROM vnpt_dev.states
WHERE name IN ('vnpt_os_saas_ordered', 'vnpt_os_saas_received', 'vnpt_os_saas_processing', 'vnpt_os_saas_completed', 'vnpt_os_saas_cancelled');

-- Xóa dữ liệu cũ nếu có
DELETE FROM vnpt_dev.state_transition_item_triggers
WHERE state_transition_item_id IN (
    SELECT id FROM vnpt_dev.state_transition_items
    WHERE state_transition_id = (SELECT id FROM temp_transition_id)
      AND state_id IN (SELECT id FROM temp_state_ids)
);

DELETE FROM vnpt_dev.state_transition_items
WHERE state_transition_id = (SELECT id FROM temp_transition_id)
  AND state_id IN (SELECT id FROM temp_state_ids);

-- Insert dữ liệu cho trạng thái vnpt_os_saas_ordered
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_ordered';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           NULL,
           '[{
     "api": null,
     "type": "RULE_ENGINE",
     "manual": null,
     "webhook": null,
     "schedule": null,
     "ruleEngine": {
       "conditions": [
         {
           "id": 1,
           "key": "1",
           "ifconds": [
             {
               "id": 1,
               "key": "1",
               "data": {
                 "value": ["VNPT"]
               },
               "operator": 1,
               "operandId": 1059
             }, {
               "id": 1,
               "key": "1",
               "data": {
                 "value": ["1"]
               },
               "operator": 1,
               "operandId": 6003
             }
           ]
         }
       ]
     }
   }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái vnpt_os_saas_received
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_api_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_received';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_received';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_ordered';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_ordered';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

SELECT id INTO v_api_id
FROM vnpt_dev.state_transition_apis
WHERE api_config @> '{"method": "POST", "url": {"raw": "api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracking_order"}}'::jsonb
      AND output_mapping @> '[{"source": "data[0].trangthai_dh"}]'::jsonb
ORDER BY id DESC
    LIMIT 1;
IF v_api_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition_apis phù hợp';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', jsonb_build_object(
                'apiId', v_api_id,
                'scheduleId', v_schedule_id,
                'constraints', jsonb_build_array(
                    jsonb_build_object(
                        'field', 'trangthai_dh',
                        'values', jsonb_build_array('Mới tiếp nhận')
                    )
                )
            ),
            'type', 'API',
            'manual', null,
            'webhook', null,
            'schedule', null,
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái vnpt_os_saas_processing
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_api_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_processing';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_processing';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_received';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_received';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

SELECT id INTO v_api_id
FROM vnpt_dev.state_transition_apis
WHERE api_config @> '{"method": "POST", "url": {"raw": "api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracking_order"}}'::jsonb
      AND output_mapping @> '[{"source": "data[0].trangthai_dh"}]'::jsonb
ORDER BY id DESC
    LIMIT 1;
IF v_api_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition_apis phù hợp';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', jsonb_build_object(
                'apiId', v_api_id,
                'scheduleId', v_schedule_id,
                'constraints', jsonb_build_array(
                    jsonb_build_object(
                        'field', 'trangthai_dh',
                                                'values', jsonb_build_array(
                            'Đang điều hành thi công',
                            'Đã giao thi công',
                            'Đã thi công xong',
                            'Đã lấy dữ liệu',
                            'Khai báo tổng đài',
                            'Đang chờ hoàn công',
                            'Đối soát hồ sơ'
                        )
                    )
                )
            ),
            'type', 'API',
            'manual', null,
            'webhook', null,
            'schedule', null,
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái vnpt_os_saas_completed
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_api_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_completed';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_completed';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_processing';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_processing';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

SELECT id INTO v_api_id
FROM vnpt_dev.state_transition_apis
WHERE api_config @> '{"method": "POST", "url": {"raw": "api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracking_order"}}'::jsonb
      AND output_mapping @> '[{"source": "data[0].trangthai_dh"}]'::jsonb
ORDER BY id DESC
    LIMIT 1;
IF v_api_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition_apis phù hợp';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', jsonb_build_object(
                'apiId', v_api_id,
                'scheduleId', v_schedule_id,
                'constraints', jsonb_build_array(
                    jsonb_build_object(
                        'field', 'trangthai_dh',
                        'values', jsonb_build_array('Đã hoàn thành')
                    )
                )
            ),
            'type', 'API',
            'manual', null,
            'webhook', null,
            'schedule', null,
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái vnpt_os_saas_cancelled
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
    v_schedule_id bigint;
    v_api_id bigint;
    v_triggers jsonb;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
IF v_transition_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition phù hợp';
END IF;

SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_cancelled';
IF v_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_cancelled';
END IF;

SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'vnpt_os_saas_processing';
IF v_pre_state_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state với tên vnpt_os_saas_processing';
END IF;

SELECT id INTO v_schedule_id FROM vnpt_dev.task_schedules WHERE code = 'SCH000001' LIMIT 1;
IF v_schedule_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy task_schedule với code SCH000001';
END IF;

SELECT id INTO v_api_id
FROM vnpt_dev.state_transition_apis
WHERE api_config @> '{"method": "POST", "url": {"raw": "api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracking_order"}}'::jsonb
      AND output_mapping @> '[{"source": "data[0].trangthai_dh"}]'::jsonb
ORDER BY id DESC
    LIMIT 1;
IF v_api_id IS NULL THEN
        RAISE EXCEPTION 'Không tìm thấy state_transition_apis phù hợp';
END IF;

    -- Tạo triggers jsonb sử dụng jsonb_build_object
    v_triggers := jsonb_build_array(
        jsonb_build_object(
            'api', jsonb_build_object(
                'apiId', v_api_id,
                'scheduleId', v_schedule_id,
                'constraints', jsonb_build_array(
                    jsonb_build_object(
                        'field', 'trangthai_dh',
                        'values', jsonb_build_array('Thoái trả')
                    )
                )
            ),
            'type', 'API',
            'manual', null,
            'webhook', null,
            'schedule', null,
            'ruleEngine', null
        )
    );

    -- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           v_triggers,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Xóa temporary tables sau khi sử dụng
DROP TABLE IF EXISTS temp_transition_id;
DROP TABLE IF EXISTS temp_state_ids;

--OS third_party---------------------------------------------------------------------------------------------------------------------------------
-- Tạo temporary table để lưu trữ state_transition_id
CREATE TEMP TABLE IF NOT EXISTS temp_transition_id AS
SELECT id FROM vnpt_dev.state_transitions
WHERE name = 'Chuyển đổi trạng thái hàng hóa kỹ thuật số' AND object_type = 'DIGITAL';

-- Tạo temporary table để lưu trữ state_ids
CREATE TEMP TABLE IF NOT EXISTS temp_state_ids AS
SELECT id, name FROM vnpt_dev.states
WHERE name IN ('third_party_os_saas_ordered', 'third_party_os_saas_received', 'third_party_os_saas_processing', 'third_party_os_saas_completed', 'third_party_os_saas_cancelled');

-- Xóa dữ liệu cũ nếu có
DELETE FROM vnpt_dev.state_transition_item_triggers
WHERE state_transition_item_id IN (
    SELECT id FROM vnpt_dev.state_transition_items
    WHERE state_transition_id = (SELECT id FROM temp_transition_id)
      AND state_id IN (SELECT id FROM temp_state_ids)
);

DELETE FROM vnpt_dev.state_transition_items
WHERE state_transition_id = (SELECT id FROM temp_transition_id)
  AND state_id IN (SELECT id FROM temp_state_ids);

-- Insert dữ liệu cho trạng thái third_party_os_saas_ordered
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_ordered';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           NULL,
           '[{
            "api": null,
            "type": "RULE_ENGINE",
            "manual": null,
            "webhook": null,
            "schedule": null,
            "ruleEngine": {
              "conditions": [
                {
                  "id": 1,
                  "key": "1",
                  "ifconds": [
                    {
                      "id": 1,
                      "key": "1",
                      "data": {
                        "value": ["THIRD_PARTY"]
                      },
                      "operator": 1,
                      "operandId": 1059
                    }, {
                      "id": 1,
                      "key": "1",
                      "data": {
                        "value": ["1"]
                      },
                      "operator": 1,
                      "operandId": 6003
                    }
                  ]
                }
              ]
            }
          }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái third_party_os_saas_received
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_received';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_ordered';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
             "api": null,
             "type": "MANUAL",
             "manual": {
               "roles": ["FULL_ADMIN"],
               "agentTypes": ["ADMIN"]
             },
             "webhook": null,
             "schedule": null,
             "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái third_party_os_saas_processing
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_processing';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_received';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
             "api": null,
             "type": "MANUAL",
             "manual": {
               "roles": ["FULL_ADMIN"],
               "agentTypes": ["ADMIN"]
             },
             "webhook": null,
             "schedule": null,
             "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái third_party_os_saas_completed
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_completed';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_processing';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
             "api": null,
             "type": "MANUAL",
             "manual": {
               "roles": ["FULL_ADMIN"],
               "agentTypes": ["ADMIN"]
             },
             "webhook": null,
             "schedule": null,
             "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái third_party_os_saas_cancelled
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_cancelled';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'third_party_os_saas_processing';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
             "api": null,
             "type": "MANUAL",
             "manual": {
               "roles": ["FULL_ADMIN"],
               "agentTypes": ["ADMIN"]
             },
             "webhook": null,
             "schedule": null,
             "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Xóa temporary tables sau khi sử dụng
DROP TABLE IF EXISTS temp_transition_id;
DROP TABLE IF EXISTS temp_state_ids;

COMMIT;
