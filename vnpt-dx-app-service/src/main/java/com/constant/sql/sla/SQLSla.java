package com.constant.sql.sla;

public class SQLSla {
    public static final String GET_GROUPED_ATTRIBUTES =
        "SELECT\n" +
                "    sa.object_type AS objectType,\n" +
                "    CAST(jsonb_agg(\n" +
                "        jsonb_build_object(\n" +
                "            'id', sa.id,\n" +
                "            'name', sa.name,\n" +
                "            'type', sa.type,\n" +
                "            'objectType', sa.object_type,\n" +
                "            'label', sa.label,\n" +
                "            'status', sa.status\n" +
                "        )\n" +
                "    ) AS text) AS attributesStr\n" +
                "FROM {h-schema}sla_attributes sa\n" +
                "GROUP BY sa.object_type\n" +
                "ORDER BY sa.object_type";
    
    public static final String GET_LIST_METRIC_GROUP =
            "select smg.id, smg.name, smg.description, smg.status as statusInt, u.name as createdBy, smg.modified_at as modifiedAt from \n" +
                    "{h-schema}sla_metric_groups smg\n" +
                    "left join {h-schema}users u ON smg.created_by = u.id \n" +
                    "where \n" +
                    " (:value = '' or (1 = :isName and smg.name ilike ('%' || :value || '%')))\n" +
                    " and (:createdBy = -1 or u.id = :createdBy)\n" +
                    " and (:status =  -1 or smg.status = :status)";

    public static final String GET_LIST_SLA_TEMPLATE_CBB =
            "select id,name from {h-schema}sla_templates where status = 1 and deleted_flag = 1";

    public static final String GET_LIST_CREATED_BY_METRIC_GROUP =
            "select distinct u.id, u.name from {h-schema}sla_metric_groups smg\n" +
                    "join {h-schema}users u ON smg.created_by = u.id \n" +
                    "where :name = '' or u.name ilike ('%' || :name || '%')\n";

}
