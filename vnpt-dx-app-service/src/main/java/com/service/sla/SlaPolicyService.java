package com.service.sla;

import com.dto.sla.IGetListPolicies;
import com.onedx.common.constants.enums.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

import com.dto.sla.SlaPolicyCreateDTO;

public interface SlaPolicyService {
    Long createSlaPolicy(SlaPolicyCreateDTO dto);

    Long updateSlaPolicy(SlaPolicyCreateDTO dto, Long id);

    /**
     * L<PERSON><PERSON> danh sách chính sách theo nguồn dữ liệu
     */
    Page<IGetListPolicies> getListPolicies(Integer isPoliciesName, String value, List<Status> status,
                                           Long createdBy, Date startDate, Date endDate, Pageable pageable);

    /**
     * Xóa SLA Policies
     * @param id ID của SLA Policies cần xóa
     */
    void deletePolicies(Long id);

    /**
     * <PERSON>idate tên ch<PERSON>h sách
     */
    void validateName(String name, Long id);
}
