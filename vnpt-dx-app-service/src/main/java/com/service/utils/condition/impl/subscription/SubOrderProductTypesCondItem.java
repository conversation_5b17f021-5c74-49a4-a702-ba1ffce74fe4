package com.service.utils.condition.impl.subscription;

import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.jsonObject.McIfConditionDTO;

import java.util.Objects;

public class SubOrderProductTypesCondItem extends CondItemBase {

    public SubOrderProductTypesCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
    }

    @Override
    public String getCondition(Integer objectType) {
        String baseSql = super.getConditionByServiceOwnerType();
        if (Objects.equals(CrmObjectTypeEnum.SUBSCRIPTION.getValue(), objectType)) {
            return String.format("service_id in (select id from vnpt_dev.services where %s)", baseSql);
        } else if (Objects.equals(CrmObjectTypeEnum.SERVICE.getValue(), objectType)) {
            return baseSql;
        }
        return "false";
    }
}
