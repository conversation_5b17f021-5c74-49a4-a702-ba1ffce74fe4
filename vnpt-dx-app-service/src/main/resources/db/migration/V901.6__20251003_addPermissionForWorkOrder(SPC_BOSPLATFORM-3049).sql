-- Thêm permission loại trạng thái
DELETE FROM vnpt_dev.permission WHERE code IN ('WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI', 'WORK_ORDER_XEM_DANH_SACH_LOAI_TRANG_THAI', 'WORK_ORDER_XEM_CHI_TIET_LOAI_TRANG_THAI','WORK_ORDER_TAO_LOAI_TRANG_THAI', 'WORK_ORDER_CHINH_SUA_LOAI_TRANG_THAI', 'WORK_ORDER_XOA_LOAI_TRANG_THAI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
(
(SELECT max(id) + 1 from vnpt_dev.permission),
'Quản lý loại trạng thái công việc',
'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI',
-1,
(SELECT max(priority) + 1 from vnpt_dev.permission)
);
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách loại trạng thái công việc',
      'WORK_ORDER_XEM_DANH_SACH_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết loại trạng thái công việc',
      'WORK_ORDER_XEM_CHI_TIET_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo loại trạng thái công việc',
      'WORK_ORDER_TAO_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa loại trạng thái công việc',
      'WORK_ORDER_CHINH_SUA_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa loại trạng thái công việc',
      'WORK_ORDER_XOA_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
  );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  );

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_LOAI_TRANG_THAI'),
                0
            );

-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_LOAI_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_LOAI_TRANG_THAI'),
                0
            );

-- Thêm permission trạng thái
DELETE FROM vnpt_dev.permission WHERE code IN ('WORK_ORDER_QUAN_LY_TRANG_THAI', 'WORK_ORDER_XEM_DANH_SACH_TRANG_THAI', 'WORK_ORDER_XEM_CHI_TIET_TRANG_THAI', 'WORK_ORDER_TAO_TRANG_THAI',
'WORK_ORDER_CHINH_SUA_TRANG_THAI', 'WORK_ORDER_XOA_TRANG_THAI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
(
(SELECT max(id) + 1 from vnpt_dev.permission),
'Quản lý trạng thái công việc',
'WORK_ORDER_QUAN_LY_TRANG_THAI',
-1,
(SELECT max(priority) + 1 from vnpt_dev.permission)
);
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách trạng thái công việc',
      'WORK_ORDER_XEM_DANH_SACH_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết trạng thái công việc',
      'WORK_ORDER_XEM_CHI_TIET_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo trạng thái công việc',
      'WORK_ORDER_TAO_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa trạng thái công việc',
      'WORK_ORDER_CHINH_SUA_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa trạng thái công việc',
      'WORK_ORDER_XOA_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
  );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  );

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_TRANG_THAI'),
                0
            );

-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUAN_LY_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_TRANG_THAI'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_TRANG_THAI'),
                0
            );

--- Thêm permission Quy trình trạng thái
DELETE FROM vnpt_dev.permission WHERE code IN ('WORK_ORDER_QUY_TRINH_TRANG_THAI', 'WORK_ORDER_XEM_DANH_SACH_QUY_TRINH_TRANG_THAI',
                                               'WORK_ORDER_XEM_CHI_TIET_QUY_TRINH_TRANG_THAI', 'WORK_ORDER_TAO_QUY_TRINH_TRANG_THAI', 'WORK_ORDER_CHINH_SUA_QUY_TRINH_TRANG_THAI', 'WORK_ORDER_XOA_QUY_TRINH_TRANG_THAI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Quy trình trạng thái',
        'WORK_ORDER_QUY_TRINH_TRANG_THAI',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
(
  (SELECT max(id) + 1 from vnpt_dev.permission),
  'Xem danh sách quy trình trạng thái',
  'WORK_ORDER_XEM_DANH_SACH_QUY_TRINH_TRANG_THAI',
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT max(priority) + 1 from vnpt_dev.permission)
),
(
  (SELECT max(id) + 2 from vnpt_dev.permission),
  'Xem chi tiết quy trình trạng thái',
  'WORK_ORDER_XEM_CHI_TIET_QUY_TRINH_TRANG_THAI',
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT max(priority) + 2 from vnpt_dev.permission)
),
(
  (SELECT max(id) + 3 from vnpt_dev.permission),
  'Tạo quy trình trạng thái',
  'WORK_ORDER_TAO_QUY_TRINH_TRANG_THAI',
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT max(priority) + 3 from vnpt_dev.permission)
),
(
  (SELECT max(id) + 4 from vnpt_dev.permission),
  'Chỉnh sửa quy trình trạng thái',
  'WORK_ORDER_CHINH_SUA_QUY_TRINH_TRANG_THAI',
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT max(priority) + 4 from vnpt_dev.permission)
),
(
  (SELECT max(id) + 5 from vnpt_dev.permission),
  'Xóa quy trình trạng thái',
  'WORK_ORDER_XOA_QUY_TRINH_TRANG_THAI',
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT max(priority) + 5 from vnpt_dev.permission)
);

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
  (SELECT max(id) + 1 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
),
(
  (SELECT max(id) + 2 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
),
(
  (SELECT max(id) + 3 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
),
(
  (SELECT max(id) + 4 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
),
(
  (SELECT max(id) + 5 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
),
(
  (SELECT max(id) + 6 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
);

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
  (SELECT max(id) + 1 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
),
(
  (SELECT max(id) + 2 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
),
(
  (SELECT max(id) + 3 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
),
(
  (SELECT max(id) + 4 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
),
(
  (SELECT max(id) + 5 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
),
(
  (SELECT max(id) + 6 from vnpt_dev.permission_portal),
  (SELECT id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_QUY_TRINH_TRANG_THAI'),
  (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
);

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
        (
            (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_QUY_TRINH_TRANG_THAI'),
            0
        );

-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
        (
            (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_DANH_SACH_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XEM_CHI_TIET_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_TAO_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_CHINH_SUA_QUY_TRINH_TRANG_THAI'),
            0
        ),
        (
            (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
            (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
            (select id from vnpt_dev.permission WHERE code = 'WORK_ORDER_XOA_QUY_TRINH_TRANG_THAI'),
            0
        );

-- Thêm permission quản lý hạng mục công việc
DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_HANG_MUC_CONG_VIEC', 'XEM_DANH_SACH_HANG_MUC_CONG_VIEC',
'XEM_CHI_TIET_HANG_MUC_CONG_VIEC', 'TAO_HANG_MUC_CONG_VIEC', 'CHINH_SUA_HANG_MUC_CONG_VIEC', 'XOA_HANG_MUC_CONG_VIEC');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
(
(SELECT max(id) + 1 from vnpt_dev.permission),
'Quản lý hạng mục công việc',
'QUAN_LY_HANG_MUC_CONG_VIEC',
-1,
(SELECT max(priority) + 1 from vnpt_dev.permission)
);
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách loại công việc',
      'XEM_DANH_SACH_HANG_MUC_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết hạng mục công việc',
      'XEM_CHI_TIET_HANG_MUC_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo hạng mục công việc',
      'TAO_HANG_MUC_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa hạng mục công việc',
      'CHINH_SUA_HANG_MUC_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa hạng mục công việc',
      'XOA_HANG_MUC_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
  );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_HANG_MUC_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  );

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'TAO_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XOA_HANG_MUC_CONG_VIEC'),
                0
            );

-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'TAO_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_HANG_MUC_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XOA_HANG_MUC_CONG_VIEC'),
                0
            );

-- Thêm permission loại công việc
DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_LOAI_CONG_VIEC', 'XEM_DANH_SACH_LOAI_CONG_VIEC', 'XEM_CHI_TIET_LOAI_CONG_VIEC', 'TAO_LOAI_CONG_VIEC',
'CHINH_SUA_LOAI_CONG_VIEC', 'XOA_LOAI_CONG_VIEC');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
(
(SELECT max(id) + 1 from vnpt_dev.permission),
'Quản lý loại công việc',
'QUAN_LY_LOAI_CONG_VIEC',
-1,
(SELECT max(priority) + 1 from vnpt_dev.permission)
);

-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách loại công việc',
      'XEM_DANH_SACH_LOAI_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết loại công việc',
      'XEM_CHI_TIET_LOAI_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo loại công việc',
      'TAO_LOAI_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa loại công việc',
      'CHINH_SUA_LOAI_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa loại công việc',
      'XOA_LOAI_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
  );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_LOAI_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  );

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'TAO_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XOA_LOAI_CONG_VIEC'),
                0
            );

-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'TAO_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_LOAI_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XOA_LOAI_CONG_VIEC'),
                0
            );

-- Thêm permission quản lý công việc
DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CONG_VIEC', 'XEM_DANH_SACH_CONG_VIEC', 'XEM_CHI_TIET_CONG_VIEC', 'TAO_CONG_VIEC',
'CHINH_SUA_CONG_VIEC', 'XOA_CONG_VIEC');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
(
(SELECT max(id) + 1 from vnpt_dev.permission),
'Quản lý công việc',
'QUAN_LY_CONG_VIEC',
-1,
(SELECT max(priority) + 1 from vnpt_dev.permission)
);

-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách công việc',
      'XEM_DANH_SACH_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết công việc',
      'XEM_CHI_TIET_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo công việc',
      'TAO_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa công việc',
      'CHINH_SUA_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa công việc',
      'XOA_CONG_VIEC',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
  );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
  );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
  (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  ),
  (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_CONG_VIEC'),
      (SELECT id from vnpt_dev.portal WHERE name = 'DEV')
  );

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'TAO_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
                (select id from vnpt_dev.permission WHERE code = 'XOA_CONG_VIEC'),
                0
            );

-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
            (
                (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'TAO_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_CONG_VIEC'),
                0
            ),
            (
                (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
                (select id from vnpt_dev.role WHERE name = 'FULL_DEV'),
                (select id from vnpt_dev.permission WHERE code = 'XOA_CONG_VIEC'),
                0
            );

-- Cập nhật view phân quyền
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;

-- Đặt lại sequence để đảm bảo id tự tăng
CREATE SEQUENCE IF NOT EXISTS roles_permissions_id_seq;
ALTER TABLE "vnpt_dev"."roles_permissions" ALTER COLUMN id SET DEFAULT nextval('roles_permissions_id_seq');
ALTER SEQUENCE vnpt_dev.roles_permissions_id_seq OWNED BY vnpt_dev.roles_permissions.id;
SELECT setval('roles_permissions_id_seq', COALESCE(max(id), 0)) FROM vnpt_dev.roles_permissions;