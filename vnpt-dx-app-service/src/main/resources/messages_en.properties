error.length=Less than {0} and great than {1}.
error.object.not.found={0} (with {1} = {2}) does not exist
error.cannot.update.because.activity.log.processed = cannot update because activity log processed
error.pricing.not.found={0} pricing not found.
error.user.not.found={0} user not found.
error.combo.plan.not.found={0} combo plan not found.
error.aff.max = level affiliate is max
error.aff.code.null = affiliate users is null
error.aff.delete = affiliate users is delete
error.aff.not.active = affiliate users is not active
error.exist.aff.com.event = exist affiliate commission event
error.aff.bill.time.null = time is null
error.aff.bill.null = bill commission is null
error.aff.parent.not.approved = affiliate parent is not approved
error.aff.parent.not.active = affiliate parent is not active
error.parent.is.failed = category parent is failed
error.aff.not.parent = affiliate is not parent
error.no.have.access={0} no have access.
error.connect.to.api.dhsxkd=have error connect to dhsxkd
error.permission.denied = permission denied
error.service.approve.config.invalid = permission denied
error.object.layout.template.dont.delete = layout template dont delete
error.object.layout.template.can.not.disable = layout template can not disable
payment.status.success = Transaction OK
error.field.must.be.not.null=Field `{0}` must be not null or empty
error.invalid.field=field {0} invalid
error.duplicate.name=Duplicate name.
error.duplicate.value= Value of {0} is duplicated
error.duplicate.code=Duplicate code.
error.duplicate.slug.name=Duplicate slugName.
error.duplicate = Duplicate {0}
error.duplicate.pricing = Duplicate.pricing {0}
error.duplicate.combo = Duplicate.combo {0}
error.duplicate.pricing.try = Duplicate.pricing.try {0}
error.duplicate.service = Duplicate.service {0}
error.duplicate.service.try = Duplicate.service.try {0}
error.api.exception=Exception was occurred while calling `{0}`
error.subscription.invalid.register.item=Subscription item must be in (device/ variant/ SaaS pricing/ combo plan)
error.subscription.buy.only.service.but.has.variant=Buying DEVICE_WITHOUT_VARIANT but the service ({0}) has variant(s)

error.no.editing.permission = error.no.editing.permission {0}
error.pricing.not.tried = error.pricing.not.tried {0}
error.field.wrong.format={0} wrong format.
error.field.invalid={0} invalid.
error.pricing.official.not.found = {0} pricing official not found.
error.you.used.pricing= you used comboPlan.
error.you.used.pricing.try= you used comboPlan try.
error.object.being.used.in.another.object=WARNING your object being used in another object.
error.can.not.delete.has.child=The record could not be deleted because of has child.
error.can.not.delete.association=The record could not be deleted because of an association.
error.state.can.not.exists=State can not exists.
error.can.not.update=Object can not update.
error.can.not.delete=Object can not delete.
error.object.is.default=Object is default.
error.invalid.file=Invalid file.
error.invalid.quarter = Invalid quarter.
error.wrong.type=Wrong type.
error.field.too.long={0} too long.
error.unsupported={0} unsupported {1}.
error.io=IO.
error.field.must.be.null=Field `{0}` must be null
error.invalid.status=Invalid status.
error.greater.than.0 = Must be greater than 0.
error.service.is.not.owned = Service is not owned.
error.service.is.not.found = Service {0} is not found.
error.service.approve.status.can.not.change = Service approve status can not change.
error.service.language.not.match = Service language must be 0 or 1.
error.service.priority.not.consecutive = Priority should be consecutive.
error.service.status.can.not.change = Service status can not change.
error.subscription.plan.are.too.quantity = Subscription plan are too quantity.
error.user.is.not.managed.by.you = User is not managed by you.
error.invalid.data=Invalid data `{0}`
error.item.not.latest.version=Item with id `{0}` is not the latest version
error.package.not.belong.to.solution=Package `{0}` does not belong to solution `{1}`
error.not.supported={0} with {1} = {2} is not supported
error.package.not.registered=Package not registered
error.invalid.configuration = System configuration {0} is invalid
error.data.out.of.range = Data value is out of range
error.invalid.external.file.link = Link of file invalid.
error.service.has.been.registered = Service has registered 1 package.
error.must.be.number = Must be number.
error.invalid.authentication = Invalid authentication.
error.invalid.subscription.plan = Subscription plan is not of service.
error.invalid.recommended.status = Invalid recommend status.
error.swap.combo.plan.order.combo = Subscription combo order can not swap combo plan
error.pricing.multi.plan.id.must.require = PricingId {0} must have multi plan id.
error.pricing.multi.plan.id.disabled = PricingMultiPlanId {0} is disabled.
error.service.disable.multi.sub = Service {0} is disabled multi subs
error.service.group.disable.multi.sub = Service group is disabled multi subs
error.service.group.unavailable.device.product = Service group does not exist Device product not available
error.combo.disable.multi.sub = Combo {0} is disabled multi subs
error.pricing.multi.plan.id.invalid = MultiPlanID {0} is invalid for PricingID {1}
error.addon.multi.plan.id.must.require = AddonId {0} must have multi plan id.
error.addon.multi.plan.not.found = AddonId {0} not found multi plan info.
error.addons.tax.invalid = {0} taxes invalid.
error.unit.invalid = {0} units invalid.
error.currency.invalid = {0} currencies invalid.
error.addon.minimum.quantity.invalid = Addon {0} is invalid minimum quantity
error.addon.maximum.quantity.invalid = Addon {0} is invalid maximum quantity
error.addon.quantity.invalid = Invalid quantity for addon {0}
error.pricing.multi.plan.minimum.quantity.invalid = Pricing multi plan {0} is invalid minimum quantity
error.pricing.multi.plan.maximum.quantity.invalid = Pricing multi plan {0} is invalid maximum quantity
error.pricing.multi.plan.quantity.invalid = Invalid quantity for pricing multiplan {0}
error.coupon.quantity.invalid=Invalid applied coupon quantity for {0} (id {1})

error.service.user.access.denied = User access denied to {0}
error.object.not.used = User not used the service.
error.masoffer.authorization.invalid = Authorization invalid: {0}
error.masoffer.data.invalid = Data invalid
#Category
error.category.wrong.type=Wrong category type.
error.category.can.not.update=Can not update.
error.system.can.not.allow.offline.payments = Can not allow offline payment in this subscription
error.system.can.not.support.payment.method = Can not support this payment method for subscription,

#traffic
error.traffic.authenticated.wallet = Traffic wallet is authenticated

#Ticket
error.ticket.has.been.assigned=Ticket has been assigned.
error.ticket.supporter.not.found = Supporter not found.
error.ticket.has.been.resolved = Ticket has been resolved.
error.ticket.invalid.object.type = Invalid object type.
error.ticket.must.be.open = Ticket must be open.
error.ticket.must.not.be.resolved = Ticket must not be resolved.
error.ticket.must.be.in.progress = Ticket must be in progress.
error.ticket.user.not.be.supporter = Current user not be supporter of ticket.
error.ticket.sme.not.be.owner = SME not be owner of ticket.
error.ticket.response.not.be.owner = User not be owner of ticket response.
error.ticket.status.not.open = Status of ticket not must open.
#Admin report
error.object.invalid.param=Parameter {0} invalid.
error.object.not.found.param=Parameter {0} not found.

#Validate annotation
error.valid.assert.false     = Must be false.
error.valid.assert.true      = Must be true.
error.valid.decimal.max      = Must be less than ${inclusive == true ? 'or equal to ' : ''}{value}.
error.valid.decimal.min      = Must be greater than ${inclusive == true ? 'or equal to ' : ''}{value}.
error.valid.digits           = Numeric value out of bounds (<{integer} digits>.<{fraction} digits> expected).
error.valid.email            = Must be a well-formed email address.
error.valid.future           = Must be a future date.
error.valid.future.or.present= Must be a date in the present or in the future.
error.valid.max              = Must be less than or equal to {1}.
error.valid.min              = Must be greater than or equal to {1}.
error.valid.negative         = Must be less than 0.
error.valid.negative.or.zero = Must be less than or equal to 0.
error.valid.not.blank        = Must not be blank.
error.valid.not.empty        = Must not be empty.
error.valid.null             = Must be null.
error.valid.not.null         = Field cannot NULL.
error.valid.range            = Must be between {2} and {1}.
error.valid.past             = Must be a past date.
error.valid.past.or.present  = Must be a date in the past or in the present.
error.valid.pattern          = Must match "{1}".
error.valid.positive         = Must be greater than 0.
error.valid.positive.or.zero = Must be greater than or equal to 0.
error.valid.size             = Size must be between {2} and {1}.
error.valid.phone            = Phone format is incorrect.
error.valid.url              = URL format is incorrect.
error.valid.length           = Size must be between {2} and {1}.
error.data.format            = Invalid data format
error.valid.phone.pattern    =Invalid phone number
error.employee.inactive=The current user is inactive.
item.not.found=Item not found with ProductId: {0} and ProductType: {1}
item.not.found.by.product.info=No matching item found for ProductId: {0} and ProductType: {1}
order.item.mismatch=Order item does not match with ProductId: {0} and ProductType: {1}
error.addons.not.in.pricing=Addon not in pricing
#Notification
error.notfound.notification = Notification not found.
error.user.not.own.notification = User not own notification.
error.maximim.comment.response  = This comment only has 1 maximum response

#Rating
error.rating.comment.reply.not.owner = User not be owner of response comment.
error.rating.not.like.your.own.comment = User can't like your own comment.
error.rating.user.not.be.developer.admin = Current user not be developer admin.

#Department
error.department.user.not.own = User not be owner of department.
error.department.active = Department cannot delete because of active status.
error.department.active.employee = Unable to disable the department due to active employee.
error.department.active.sub.department = Unable to disable the department due to active sub-department.
error.inactive.department.can.not.create = Inactive department cannot create because it have active employee.
error.active.department.can.not.create = Active department cannot create because it have inactive employee.
error.duplicate.name.by.parent = Duplicate name in a parent department.
error.department.has.child = Department cannot delete because it has child.
error.parent.department.not.found = Parent department not found.
error.parent.department.inactive = Parent department inactive.
error.department.can.not.save = Department cannot save because of inactive parent department.
error.department.not.in.same.sme = Not in the same SME
error.current.user.not.same.company = User login and createdBy are not same company.

#Currency
error.currency.used = This currency is being used.

#Unit
error.object.used = Unit is used, can not delete

#variant
error.default.status.not.null = variant default is null
error.default.status.is.multi = variant default is multi
error.not.exist.variant = variant not exist

#Coupon
error.not.approve = Approve status can not change.
error.object.need.approve = Coupon need approve.
error.object.need.unapprove = {0} need unapprove.
error.coupon.user.not.own = User not be owner of coupon.
error.data.exists = {0} exists.
error.data.exists.not.active = {0} exists not active.
error.url.exists = url exists.
error.multisub.not.allowed=Multi-sub is not allowed by {0}
error.email.exists = email exists.
error.phone.exists = phone exists.
error.email.exists.in.system = email exists admin.
error.tin.exists = tin exists.
error.rep.personal.cert.number.exists = repPersonalCertNumber exists.
error.business.license.number.exists = businessLicenseNumber exists.
error.public.addon.greater.than.max.accepted.value = public addons size greater than 30.
error.private.addon.greater.than.max.accepted.value = private addons of pricing_multi_plan_id={0} size greater than 30.
error.end.date.must.after.start.date = end day must be after start date.
error.less.than = {0} must be less than {1}
error.invalid.approve= Coupon not must unApproved.
error.statusApprove.notChange= Approve status must not awaittingApproval.
error.coupon.has.been.used = Coupon has been used.


#Feature
error.feature.still.used = Feature still used.

# Tax
error.not.change = This {0} cannot be edited
error.object.still.used = {0} is used, can not delete
error.object.still.registered = {0} is registered, can not delete

#Service
error.service.invalid = Service {0} invalid.
error.not.exist.service = service is not exist
error.service.not.active = service not active
error.exists.services.sku = sku is exists
error.default.sku.not.null = sku must be not null

#Service_group
error.service.not.customer.in.service.group = There is a product or service that belongs to a group that does not belong to the customer group. Please check again!

#Pricing
error.pricing.not.be.draft = Pricing not be draft.
error.pricing.not.be.awaiting.approval = Pricing not be awaiting approval.
error.pricing.not.awaiting.approval = Pricing status cannot awaiting approval.
error.object.need.approved= Pricing need approved
error.user.can.not.delete = User can not delete.
error.user.not.have.permission = User does not have permission.
error.pricing.still.used = Pricing still used.
error.combo.using.pricing = Combo using pricing.
error.pricing.coupon.approve = Coupon approved, so cannot delete pricing.
error.coupon.out.of.quantity = Coupon {0} out of quantity.
error.pricing.addons.invalid = Invalid Pricing.
error.combo.pricing.invalid = A combo plan requires at least 2 service.
error.service.version.invalid=Service with draftId `{0}` is not the latest version
error.pricing.ver.invalid = Version of pricing {0} invalid.
error.combo.plan.ver.invalid = Version of combo plan {0} invalid.
error.combo.ver.invalid = Version of combo {0} invalid.
error.addon.ver.invalid = Version of addon {0} invalid.
error.services.invisible=Service (id = {0}) is invisible
error.combo.invisible = {0}
error.object.deleted = {0} deleted.
error.object.inactive = {0} inactive.
error.object.active = {0} active.
error.item.in.used = {0} is used and cannot be inactive .
error.item.in.registered= {0} is registered and cannot be inactive .
error.tax.invalid = Invalid taxList.
error.tax.invalid.over.range = Invalid taxList over range.
error.coupon.pricing.invalid = Invalid couponPricing.
error.coupon.pricing.apply.invalid = Invalid couponPricingApply.
error.coupon.enterprise.invalid=Cannot apply coupon `{0}` on user `{1}`
error.coupon.suppliers.invalid = Invalid couponSuppliers.
error.coupon.addons.invalid = Invalid couponAddons.
error.payment.cycle.pricing.greater.than.or.equal.addon = Invalid cycleType.
error.cannot.change.pricing.of.another.service = Cannot change pricing of another service.
error.pricing.customer.type.code.invalid = Pricing customer code invalid.
error.invalid.customer.type=Invalid customer type (requested `{0}` but must be `{1}`)
error.invalid.field.can.not.edit = Field cannot be editted
error.pricing.plan.have.customer.type.invalid = ID pricing_plan have customer_type invalid: {0}
error.pricing.not.belong.to.service = Pricing ID {0} does not belong to service ID {1}
error.shoppingCart.invalid.pricing.status = Services or Pricing has been deleted or turned off status.

# Addon
error.addon.not.be.awaiting.approval = Addon not be awaiting approval.
error.user.can.not.approve = Current user cannot approve.
error.addon.still.used = Addons still used.
error.addon.user.not.own = Current user not own addon.
error.coupon.invalid.expired = Coupon has expired.
error.out.of.quantity = Out of quantity.
error.addon.need.approved = Addon need approved
success.deleted.all.addon = All addon has been deleted.
success.deleted.addons.non.subscription = Addon non subscription has been deleted.
error.addon.must.not.unApproved = Addon must not unApproved.
error.addon.need.required = Addon required incorrect.
success.deleted.all.service = All service has been deleted.
success.deleted.service.non.used = Service non use has been deleted.
error.service.still.used = Services still used.
error.addon.in.used = Addons still used in other pricing:{0}
error.addon.payment.cycle.exists = Addons payment cycle exists.
error.addon.payment.cycle.can.not.null = Addons payment cycle must not be null.
error.shoppingCart.invalid.addon.status = Addon has been deleted or turned off status.

# Coupon
error.coupon.timesUsed.type = Times Used not supported
error.coupon.discount.type = Discount not supported
error.coupon.isVat = Is Vat not supported
error.coupon.code.type = Code type not supported"
error.coupon.enterprise.type = Enterprise type not supported
error.coupon.pricing.type = Pricing type not supported
error.coupon.type = Coupon type not supported
error.promotion.type = Promotion type not supported
error.coupon.addon.type = "Addon type not supported

error.current.user.not.be.owner = Current user not be owner.
error.can.not.use.milti.coupon = Can not use multi coupon.
error.addon.inactive = Addon inactive;
error.coupon.inactive = Coupon inactive;
error.total.subscription.incorrect = Total amount incorrect.
error.billing.incorrect = Billing info incorrect.
error.billing.cannot.update=Billing cannot update.

# Transaction
payment.status.error.not.found = Transaction not found
payment.status.error.received = Transactions that have been previously notified
payment.status.error.timeout =  Transaction timeout
payment.status.error.secure.code = Transaction signature incorrect
payment.status.error.checksum = Transaction signature incorrect
payment.status.error.amount = Amount incorrect
payment.status.error.other = Other error
payment.status.already.processed=The transaction was processed before

# subscription
error.wrong.user = Wrong user.
error.can.not.init = Can not init
error.can.not.edit.user.saas = Can not edit user saas
error.user.not.own.subscription = User not own subscription.
error.partner.not.own.order = Partner does not own order.
error.subscription.flat.rate.only.one.quantity = Flat rate only one quantity.
error.subscription.can.not.cancel = Subscription can not cancel.
error.subscription.user = Company used this subscription
error.subscription.addon.user = {0} addon were duplicate 
error.subscription.addon.service = ID service = {0}. service main package coincides with service additional package 
error.payment.can.not.change = Payment method can not change.
error.subscription.cannot.decrease.quantity = It is not allowed to reduce the quantity during use.
error.subscription.cannot.change.quantity = It is not allowed to change the quantity during use.
error.subscription.cannot.increase.quantity = It is not allowed to increase the quantity during use.
error.object.user.used = This customer is currently using the service.
error.subscription.is.not.activate.or.not.feature = Subscription's status is not feature or active.
error.subscription.is.not.owned = Subscription is not owned.
error.subscription.activated = Subscription is activated.
error.missing.certificate = Certificate Photo Missing.
error.missing.business.licence = Business Licence Photo Missing.
error.user.is.already_affiliate = User is already an affiliate.
error.user.already.role.affiliate = User is already an affiliate 2.
error.subscription.in.trial = Subscription for pricing {0} in trial.
error.pricing.does.not.support.trial = This pricing does not support trial.
error.combo.does.not.support.trial = This combo plan does not support trial.
error.object.already.synced = Object already synced.
error.subscription.trial.order.service.for.sme = Can not subscription trial order service for sme
error.have.error.from.product.control.center = Error cause by: {0}
error.remoteSystem.not.respond= Remote system `{0}` does not respond
error.remoteSystem.response.unauthorized = Remote system `{0}` responds with an unauthorized error. 
error.remoteSystem.respond.failure= Remote system `{0}` responds with failure `{1}`
error.credit.note.has.been.applied = {0}

#combo
error.combo.must.not.unApproved = Unapproved combos
error.combo.must.unApproved= combo must unApprove
user.id.must.not.be.empty= User id must not be empty
error.combo.must.awaitingApprove= Combo's status must AWAITING_APPROVE
error.combo.reject.comment.must.not.empty= Reject combo comment must not empty
error.combo.status.was.not.be.found= Status was not be found
error.combo.was.not.found= Combo was not found;
error.combo.addon.cycle.is.greater = The cycle type of combo plan must be greater than the cycle type of the addon
error.current.user.can.not.change.status = Current user can not change status.
error.combo.can.not.change.status = Combo can not change status.
error.combo.plan.is.not.approved = combo plan is not approved.
error.combo.is.not.approved = combo is not approved.
error.combo.plan.inactive = {0}
error.combo.not.found = combo is not found
error.invalid.ip.address = invalid ip address: {0}
error.combo.used = combo plan is used, can not delete
error.subscription.coupon.user = user registered coupon {0} of other subscription
error.information.user=User (ID {0}) does not have required information
error.shoppingCart.invalid.combo_plan.status = Combo or Combo_plan has been deleted or turned off status.

#Redis
error.redis.server = Redis service error.

#JSON
error.json.format.incorrect = Json format incorrect.

#Credit_note

credit.note.type.decrease = Gi\u1EA3m s\u1ED1 l\u01B0\u1EE3ng trong g\u00F3i \u0111\u00E3 \u0111\u0103ng k\u00FD
credit.note.type.increase = T\u0103ng s\u1ED1 l\u01B0\u1EE3ng trong g\u00F3i \u0111\u00E3 \u0111\u0103ng k\u00FD
credit.note.type.swap.pricing = \u0110\u1ED5i g\u00F3i d\u1ECBch v\u1EE5
credit.note.type.delete.addon = X\u00F3a d\u1ECBch v\u1EE5 b\u1ED5 sung
credit.note.type.add.addon = Th\u00EAm d\u1ECBch v\u1EE5 b\u1ED5 sung
credit.note.type.add.fee = Th\u00EAm ph\u00ED nhanh
credit.note.type.cancel = H\u1EE7y d\u1ECBch v\u1EE5 tr\u01B0\u1EDBc th\u1EDDi h\u1EA1n
credit.note.type.re.active = K\u00EDch ho\u1EA1t l\u1EA1i g\u00F3i d\u1ECBch v\u1EE5

#0: T\u00EAn g\u00F3i
#1: T\u00EAn d\u1ECBch v\u1EE5

credit.note.value.decrease = Gi\u1EA3m s\u1ED1 l\u01B0\u1EE3ng s\u1EED d\u1EE5ng trong g\u00F3i {0} c\u1EE7a d\u1ECBch v\u1EE5 {1}.
credit.note.value.increase = T\u0103ng s\u1ED1 l\u01B0\u1EE3ng s\u1EED d\u1EE5ng trong g\u00F3i {0} c\u1EE7a d\u1ECBch v\u1EE5 {1}.
credit.note.value.swap.pricing = \u0110\u1ED5i sang g\u00F3i d\u1ECBch v\u1EE5 {0} c\u1EE7a d\u1ECBch v\u1EE5 {1}.
credit.note.value.cancel = H\u1EE7y g\u00F3i d\u1ECBch v\u1EE5 {0} c\u1EE7a d\u1ECBch v\u1EE5 {1} tr\u01B0\u1EDBc th\u1EDDi h\u1EA1n.
credit.note.value.re.active = K\u00EDch ho\u1EA1t l\u1EA1i g\u00F3i d\u1ECBch v\u1EE5 {0} c\u1EE7a d\u1ECBch v\u1EE5 {1}.

#0: T\u00EAn addon
#1: T\u00EAn d\u1ECBch v\u1EE5

credit.note.value.delete.addon = X\u00F3a d\u1ECBch v\u1EE5 b\u1ED5 sung {0} ({1}).
credit.note.value.add.addon = Th\u00EAm  d\u1ECBch v\u1EE5 b\u1ED5 sung {0} ({1}).

#0: T\u00EAn ph\u00ED

credit.note.value.add.fee = Th\u00EAm ph\u00ED {0}.

#Subscription
error.subscription.billings.outOfDate = Subscription have billings out of date
error.subscription.can.not.activate.again = Subscription can not activate again
error.subscription.can.not.activate.permission = Subscription can not activate permission
error.subscription.is.unlimited = Subscription is unlimited
error.can.not.renew.subscription = Can't renew subscription
error.can.not.renew.subscription.status = Renew Failed subscription has ended
error.can.not.swap.in.final.cycle = Subscription can not swap pricing in final cycle.
error.subscription.number.of.cycles = Must be greater than or equal to current cycle.
error.can.not.cancel.onetime.subscription = Can't cancel one time subscription
error.can.not.cancel.os.subscription = Can't cancel order service subscription
error.can.not.cancel.on.limited.cycles.subscription = Can't cancel online service with limited cycles subscription
error.invalid.payment.method=Invalid payment method {0}

# MasOffer
error.masoffer.api = Has an error when send post back to MasOffer system.

# access trade
error.accesstrade.api = Has an error when send post back to Access trade system.

# apinfo
error.apinfo.api = Has an error when send post back to apinfo system.

error.coupon.invalid = Coupon {0} invalid
error.addon.invalid = Addons with names {0} in used
error.object.in.used = {0} is in used.
error.coupon.in.used = Coupon with names {0} in used
error.object.in.trial = {0} is in trial.
subscription.pricing.duplicate = Pricing is duplicated
error.coupon.not.eligible.to.use = Coupon not eligible to use:{0}

# OrderService
error.order.service.status.must.be.received = Order status must be received.
error.unknown = Unknown error.
error.order.service = service owner must be SAAS or VNPT.
error.subscription.is.cancel= Your subscription is cancel

error.subscription.conflict = D?ch v?: {0} trong g\uFFFDi combo ?\uFFFD ???c s? d?ng. B?n vui l\uFFFDng kh\uFFFDng ch?n
error.miss.data.transaction = Missing data when looking up transaction results
error.can.not.call.dhsxkd = Cannot call API

# Invoice
error.einvoice.amount.zero = Total amount of invoice = 0. Do not export einvoice
error.invoice.authen.invalid = Invalid account or do not permission add customer
error.invoice.xml.invalid = Invalid xml input- {0}
error.invoice.not.found.company = Not found company of user
error.invoice.pattern.serial.invalid = Invalid Pattern and serial
error.invoice.not.publish = Cannot publish invoice
error.invoice.call.api.import.failed = Call api import and publish invoice failed
error.invoice.already.exported = e-invoices for billing '{0}' have already been exported
error.pricing.multi.plan.is.not.empty = pricing plan is not empty.
error.package.item.is.not.empty=package items is not empty.
error.pricing.multi.plan.duplicate.payment.cycle = pricing plan duplicate payment cycle.
error.pricing.multi.plan.duplicate.cycle.code = pricing plan duplicate cycle code.
error.pricing.multi.plan.duplicate.cycle.code.all.DB = pricing plan duplicate cycle code all system.
error.pricing.multi.plan.duplicate.package.code.all.DB = pricing plan duplicate package code all system.
error.invalid.captcha.token = Invalid ReCaptcha Token.
error.seo.planUrl.invalid = Plan url already exists
error.subscription.invalid = subscription: {0} invalid
error.service.owner.other = Must choose addon have service_owner same
error.corresponding.invoice.not.found = The invoice could not be found
exceeds.100.units = Exceeds 100 units of the required quantity
error.fkey.duplicate = Fkey Duplicate error
error.invalid.can.not.change=Field {0} can not change value
#Enterprise Error
error.enterprise.ids.not.be.empty = enterpriseIds not be empty
error.enterprise.active.status.invalid = activeStatus invalid
error.enterprise.archive.status.invalid = archiveStatus invalid
error.enterprise.email.not.null = email invalid
error.enterprise.email.duplicated = email duplicated
error.birthday.greater.than.today = birthday greater than today
error.enterprise.tin.not.null = tin invalid
error.enterprise.tin.duplicated = tin duplicated
error.enterprise.name.not.null = name invalid
error.enterprise.name.duplicated = name duplicated
error.enterprise.phone.not.null = phone invalid
error.enterprise.phone.duplicated = phone duplicated
error.enterprise.general.desc.not.null = general desc invalid
error.enterprise.business.registration.urls.not.null = business registration urls invalid
error.enterprise.id.not.null = enterprise id invalid
error.enterprise.duplicate.email=Duplicate email
error.enterprise.name.not.empty = Name must not be empty
error.enterprise.subscription.existed = Deleting an enterprise that has subscription(s)
error.enterprise.personalCert.duplicated = Personal certificate duplicated
error.user.has.been.registered = Customer has been registered
#CustomerContact Error
error.customer.contact.duplicate.email= Duplicate email;
error.customer.contact.duplicate.personalPhone = Duplicate phone;
error.customer.contact.duplicate.identityno = Duplicate identityno;
error.customer.contact.duplicate.personalEmail = Duplicate personalEmail;
error.customer.contact.duplicate.tin= Duplicate tin
error.customer.contact.not.found = contactId invalid
error.customer.contact.name.null = contactName invalid
error.customer.contact.phone.null = contactPhone invalid
error.customer.contact.email.null = contactEmail invalid
error.customer.contact.tin.null = contactTin invalid
error.customer.contact.tin.duplicated = contactTin duplicated
error.customer.contact.email.duplicated = email duplicated
error.customer.contact.file.size.exceed = file size ({0} bytes) exceed 10MB
error.customer.contact.file.cannot.be.opened = file cannot be opened
error.customer.contact.file.redundant.column = redundant column {0}
error.customer.contact.file.duplicate.column = duplicate column {0}
error.customer.contact.file.missing.column = missing column {0}
error.customer.contact.file.is.empty.data = file must not be empty data
error.customer.contact.groupId.null = groupId null

error.customer.contact.contactPhones.empty=empty contact phones
error.customer.contact.contactEmails.empty=empty contact emails
error.customer.contact.customer.type.is.invalid = customer type is invalid
#CustomerGroup Error
error.customer.group.not.found = groupId invalid
error.customer.group.duplicate.name = Duplicate name
error.customer.group.enterpriseId.null = enterpriseId null
error.customer.group.uploadedCustomerId.null = uploadedCustomerId null
error.customer.group.contactId.null = contactId null
error.customer.group.duplicate.email = Duplicate email: {0}
error.customer.uploaded.ids.not.be.empty =  customerUploadedIds must not be empty data
error.customer.group.size.exceed=The number of customers in the group ({0}) exceeds the allowed number

error.mail.limited = Message submission rate for this client has exceeded the configured limit

# MarketingCampaign Errors
error.campaign.not.found = Marketing campaign (id = {0}) not found
error.campaign.invalid.json.format = Marketing campaign (id = {0}) has invalid json format
error.campaign.duplicate.name = Marketing campaign (name = {0}) currently exists
error.campaign.invalid.budget = Marketing campaign (name = {0}) has invalid budget
error.campaign.copy.from.archived.campaign = Cannot copy an archived marketing campaign (id = {0})
error.campaign.update.campaign.in.invalid.status = Cannot update marketing campaign from invalid status (status = {0})

# MCActivity Error
error.mc.activity.not.found = McActivity (id = {0}) not found
error.mc.activity.existed = McActivity (id {0}) currently exists in campaign (id {1})

# McActionEmailAutoSms Error
error.mc.action.email.auto.sms.not.found = McActionEmailAutoSms (id = {0}) not found
error.mc.action.email.auto.sms.sent = McActionEmailAutoSms (id = {0}) sent

error.subscription.can.not.update = Subscription {0} can not update.

error.product.order.can.not.update = Product order {0} can not update.

#PageBuilder Error
error.pagebuilder.active=Another page builder with slugName `{0}` is already in active state
error.pagebuilder.invalid.version=Invalid page builder version

#Transaction logs
error.cannot.update.api.url = Error cannot update api url.
error.api.url.not.null = Error api url not null.

# ShoppingCart Error
error.shoppingCart.invalid.userId = Invalid user ID '{0}'
error.shoppingCart.content.empty = Shopping cart content must not be empty

# Topic
error.topic.approve.status.can.not.change = Topic approve status can not change.
error.object.unapproved = {0} unapproved.

# Validate Excel file
error.xlsx.name.too.short = File name [{0}] is too short
error.xlsx.name.too.long = File name [{0}] is too long
error.xlsx.invalid.extension = Invalid extension
error.xlsx.file.size.exceed = File size {0} exceeds 10MB
error.xlsx.redundant.column = Column [{0}] is redundant
error.xlsx.duplicated.column = Column [{0}] is duplicated
error.xlsx.missing.column = Column [{0}] is missing

# Validate user activation
error.user.activated = User is activated
error.user.activation.key.expired = Activation Key is expired
error.user.activation.key.not.matched = Activation Key is not matched

exists={0} exists
error.exists.email.diff.enterprise.house_hold = Exists email KHDN/HKD
exists.email.other.customer.type = Exists email other customer type
error.exists.email = Exists email
error.send.mail.fail.invalid.address = Send mail failed because of invalid address {0}
error.weak.password= password is weak

error.service.group.code.existed = service group code existed
error.service.group.not.found = service group not found
error.service.group.must.awaitingApprove = service group must await approve
error.service.group.must.unApprove = service group must unapproved
error.service.exist.disable.multi.sub = Service exist disable multi sub
error.secure.code.invalid = Secure code invalid
error.subscription.canceled = Subscription has been canceled
error.subscription.code.exist = Subscription code exist
error.variant.out.of.stock=Variant (draftId {0}) is out of stock

error.variant.not.exist.pricing= Variant is not exist pricing
error.partner.invalid.token=JWT token is invalid
error.partner.expired.token=JWT token is expired

# Partner
error.partner.not.found = Partner not found
error.valid.currency.pattern=Invalid currency
error.valid.payment.method.pattern=Invalid payment method
error.invalid.transition=Invalid transition of status from {0} to {1}

# product_solutions
error.solution.code.exist = solution code existed
error.package.default.is.not.null = package default is not null
error.package.name.exist = package name existed
error.package.exist.for.subscription = package exists for subscription
error.package.not.allowed = package not allowed
error.solution.not.found = solution not found
error.package.not.found = package not found
error.solution.reject.comment.must.not.empty = solution reject comment must not empty
error.package.reject.comment.must.not.empty = package reject comment must not empty
error.package.code.exist = package code existed
error.apply.condition.not.match=Apply condition is not matched ({0} must be {1} instead of {2})

#sla
error.metric.group.name.exist = metric group name existed
error.metric.group.not.found = metric group not found
error.sla.metric.active = metric is activating
error.sla.policy.active = policies are activating
error.sla.template.active = sla template is activating
error.used.metric.template = metric is in template
error.used.sla.template.policy = sla template is in sla policy
error.sla.policy.name.exist = sla policy name existed


# Api GW KHCN
error.apigwkhcn.failure.response = Failure response
error.apigwkhcn.not.vinaphone.msisdn = Msisdn is not belong VinaPhone subscriber
error.apigwkhcn.not.prepaid.vinaphone.msisdn = Msisdn is not belong to prepaid VinaPhone subscriber
error.apigwkhcn.postpaid.vinaphone.msisdn = Msisdn is belong to postpaid VinaPhone subscriber
error.carrier.code.exist = carrier code existed

# WorkOrder
error.work_order.assign.child.as.parent = Cant assign a child work order id={0} as the parent of this work order id={1}
error.checklist.not.found.phase.and.step = Not found step code={0} in phase code={1}