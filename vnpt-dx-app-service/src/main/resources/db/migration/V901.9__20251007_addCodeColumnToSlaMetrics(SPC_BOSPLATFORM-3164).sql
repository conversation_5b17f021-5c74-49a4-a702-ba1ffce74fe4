ALTER TABLE vnpt_dev.sla_metrics
ADD COLUMN code VARCHAR(8) UNIQUE,
ADD COLUMN status SMALLINT DEFAULT 1 NOT NULL,
ADD COLUMN created_by BIGINT NOT NULL,
ADD COLUMN created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN modified_by BIGINT,
ADD COLUMN modified_at TIMESTAMP,
ADD COLUMN deleted_flag SMALLINT DEFAULT 1 NOT NULL;

-- Tạo comment cho các cột mới
COMMENT ON COLUMN vnpt_dev.sla_metrics.code IS 'Mã metric - Format: MTxxxxxx (MT + 6 số tăng dần)';
COMMENT ON COLUMN vnpt_dev.sla_metrics.status IS 'Trạng thái hoạt động (1: ACTIVE, 0: INACTIVE)';
COMMENT ON COLUMN vnpt_dev.sla_metrics.created_by IS 'ID người tạo';
COMMENT ON COLUMN vnpt_dev.sla_metrics.created_at IS 'Thời gian tạo';
COMMENT ON COLUMN vnpt_dev.sla_metrics.modified_by IS 'ID người cập nhật';
COMMENT ON COLUMN vnpt_dev.sla_metrics.modified_at IS 'Thời gian cập nhật';
COMMENT ON COLUMN vnpt_dev.sla_metrics.deleted_flag IS 'Trạng thái xóa (1: Chưa xóa, 0: Đã xóa)';
