package com.onedx.common.rsqlJPA;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;
import cz.jirutka.rsql.parser.ast.ComparisonOperator;


public class GenericRsqlSpecification<T> implements Specification<T> {
    private static final Logger log = LoggerFactory.getLogger(GenericRsqlSpecification.class);
    private final String property;
    private final ComparisonOperator operator;
    private final List<String> arguments;

    public GenericRsqlSpecification(
            String property, ComparisonOperator operator, List<String> arguments) {
        super();
        this.property = property;
        this.operator = operator;
        this.arguments = arguments;
    }

    @Override
    public Predicate toPredicate(
            Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {

        List<Object> args = castArguments(root);
        Object argument = args.get(0);
        switch (RsqlSearchOperation.getSimpleOperator(operator)) {

            case EQUAL: {
                Path path = root.<String>get(property);
                if("expression".equals(property)) {
                    String[] listArg = argument.toString().split(";");
                    for(int i = 0; i < listArg.length; i ++) {
                        if(listArg[i].indexOf("groupBy") > -1) {
                            if(!("").equals(listArg[i].split("==")[1])) {
                                query.groupBy(root.<String>get(listArg[i].split("==")[1]));
                            }
                        }
                        if(listArg[i].indexOf("orderBy") > -1) {
                            String condition = listArg[i].split("==")[1];
                            if(!("").equals(condition)) {
                                String[] conditions = condition.split(",");
                                if(conditions.length > 1 && ("desc").equals(conditions[1])) {
                                    query.orderBy(builder.desc(root.<String>get(conditions[0])));
                                }
                            }
                        }
                    }
                    break;
                }

                if (argument instanceof String) {
//                    Expression<Collection<String>> set = root.get(property);
//                    Path path = root.<String>get(property);
                    if(("null").equals(argument)) {
                        return builder.isNull(root.get(property));
                    }
                    if(path.getJavaType() == Boolean.class) {
                        String tmp = (String)argument;
                        String val = "1".equals(tmp) ? "true" : ("0".equals(tmp) ? "false" : tmp);
                        return builder.equal(root.get(property),Boolean.valueOf(val));
                    }

                    if(path.getJavaType() == Double.class) {
                        return builder.equal(root.get(property),Double.valueOf(argument.toString()));
                    }
                    if(path.getJavaType() == BigDecimal.class) {
                        // return builder.equal(root.get(property),BigDecimal.valueOf(Long.parseLong(argument.toString())));
                        return builder.equal(root.get(property),BigDecimal.valueOf(Double.valueOf(argument.toString())));
                    }

                    // check wildcard character of sql
                    // % _ [ ] ^ -

                    if(((String) argument).contains("%") ||
                            ((String) argument).contains("_") ||
                            ((String) argument).contains("[") ||
                            ((String) argument).contains("]") ||
                            ((String) argument).contains("^") ||
                            ((String) argument).contains("-")) {
                        String finalArgument = ((String) argument)
                                .replace("%", "!%")
                                .replace("_", "!_")
                                .replace("[", "![")
                                .replace("]", "!]")
                                .replace("^", "!^")
                                .replace("-", "!-");
                        return builder.like(
                                builder.lower(root.get(property))
                                , finalArgument.toLowerCase().replace("%20"," ").replace('*', '%'), '!');
                    }

                    return builder.like(
                            builder.lower(root.get(property))
                            , argument.toString().toLowerCase().replace("%20"," ").replace('*', '%'));
                } else if (argument == null) {
                    return builder.isNull(root.get(property));
                } else {
                    return builder.equal(root.get(property), argument);
                }
            }
            case NOT_EQUAL: {
                if (argument instanceof String) {
                    // check wildcard character of sql
                    // % _ [ ] ^ -

                    if(((String) argument).contains("%") ||
                            ((String) argument).contains("_") ||
                            ((String) argument).contains("[") ||
                            ((String) argument).contains("]") ||
                            ((String) argument).contains("^") ||
                            ((String) argument).contains("-")) {
                        String finalArgument = ((String) argument)
                                .replace("%", "!%")
                                .replace("_", "!_")
                                .replace("[", "![")
                                .replace("]", "!]")
                                .replace("^", "!^")
                                .replace("-", "!-");
                        return builder.notLike(
                                root.get(property), finalArgument.replace("%20"," ").replace('*', '%'), '!');
                    }

                    return builder.notLike(
                            root.get(property), argument.toString().replace("%20"," ").replace('*', '%'));
                } else if (argument == null) {
                    return builder.isNotNull(root.get(property));
                } else {
                    return builder.notEqual(root.get(property), argument);
                }
            }
            case GREATER_THAN: {
                return builder.greaterThan(root.get(property), argument.toString());
            }
            case GREATER_THAN_OR_EQUAL: {
                return builder.greaterThanOrEqualTo(
                        root.get(property), argument.toString());
            }
            case LESS_THAN: {
                return builder.lessThan(root.get(property), argument.toString());
            }
            case LESS_THAN_OR_EQUAL: {
                return builder.lessThanOrEqualTo(
                        root.get(property), argument.toString());
            }
            case IN:
                return root.get(property).in(args);
            case NOT_IN:
                return builder.not(root.get(property).in(args));
        }

        return null;
    }

    private List<Object> castArguments(Root<T> root) {
        List<Object> args = new ArrayList<Object>();
        Class<? extends Object> type = root.get(property).getJavaType();

        for (String argument : arguments) {
            if (type.equals(Integer.class)) {
                if(!("null").equals(argument)) {
                    args.add(Integer.parseInt(argument));
                }
                else {
                    args.add(null);
                }
            } else if (type.equals(Long.class)) {
                if(!("null").equals(argument)) {
                    args.add(Long.parseLong(argument));
                }
                else {
                    args.add(null);
                }
            } else {
                args.add(argument);
            }
        }

        return args;
    }
}
