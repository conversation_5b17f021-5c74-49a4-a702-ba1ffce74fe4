package com.service.utils.condition.impl.workOrders;

import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.jsonObject.McIfConditionDTO;

public class WorkOrderPriorityCondItem extends CondItemBase {

    public WorkOrderPriorityCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
    }

    @Override
    public String getCondition(Integer objectType) {
        return "wo_orders.id IN (SELECT wo_orders.id FROM vnpt_dev.wo_orders WHERE " + this.getConditionByListIDColumn("priority") + ")";
    }
}
