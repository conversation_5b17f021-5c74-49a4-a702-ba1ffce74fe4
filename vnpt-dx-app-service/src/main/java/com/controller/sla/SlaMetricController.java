package com.controller.sla;

import java.util.List;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.component.BaseController;
import com.dto.common.ICommonIdName;
import com.dto.sla.IGetListMetric;
import com.dto.sla.IGetSlaMetricGroupDTO;
import com.dto.sla.SlaMetricGroupCreateDTO;
import com.dto.sla.request.SlaMetricCreateRequestDTO;
import com.dto.sla.request.SlaMetricDeleteReqDTO;
import com.dto.sla.request.SlaMetricUpdateRequestDTO;
import com.dto.sla.request.SlaMetricValidateRequestDTO;
import com.dto.sla.response.SlaMetricDeleteResDTO;
import com.dto.sla.response.SlaMetricNameResponseDTO;
import com.dto.sla.response.SlaMetricResponseDTO;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.utils.DateUtil;
import com.service.sla.SlaMetricService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import com.enums.sla.SlaMetricTypeEnum;
import com.enums.sla.SlaObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/api/admin-portal/sla-metric")
@Slf4j
@Validated
@RequiredArgsConstructor
public class SlaMetricController {

    private final SlaMetricService slaMetricService;

    @PostMapping("/group")
    @Operation(description = "Tạo nhóm chỉ số")
    public Long createMetricGroup(@RequestBody SlaMetricGroupCreateDTO request) {
        return slaMetricService.createMetricGroup(request);
    }

    @GetMapping("/group/validate-name")
    @Operation(description = "Validate tên nhóm chỉ số")
    public void validateNameMetricGroup(
            @RequestParam(name = "name") String name,
            @RequestParam(name = "id", required = false, defaultValue = "-1") Long id
    ) {
        slaMetricService.validateNameMetricGroup(name, id);
    }

    @PutMapping("/group/{id}")
    @Operation(description = "Sửa nhóm chỉ số")
    public Long updateMetricGroup(@RequestBody SlaMetricGroupCreateDTO request, @PathVariable Long id) {
        request.setId(id);
        return slaMetricService.updateMetricGroup(request);
    }

    @GetMapping("/group")
    @Operation(description = "Danh sách nhóm chỉ số")
    public Page<IGetSlaMetricGroupDTO> getMetricGroup(
            @RequestParam(value = "isName", required = false, defaultValue = "1") Integer isName,
            @RequestParam(value = "value", required = false, defaultValue = "") String value,
            @RequestParam(value = "status", required = false, defaultValue = "UNSET") StatusEnum status,
            @RequestParam(value = "createdBy", required = false, defaultValue = "-1") Long createdBy,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "modifiedAt,desc") String sort
    ) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return slaMetricService.getMetricGroup( isName, value, status, createdBy, listRequest.getPageable());
    }

    @GetMapping("/group/created-by")
    @Operation(description = "Lấy danh sách người tạo")
    public List<ICommonIdName> getCreatedByMetricGroup(
            @RequestParam(name = "name", required = false, defaultValue = "") String name
    ) {
        return slaMetricService.getCreatedByMetricGroup(name);
    }

    @DeleteMapping("/group/{id}")
    @Operation(description = "Xóa nhóm chỉ số")
    public void deleteMetricGroup(@PathVariable Long id) {
        slaMetricService.deleteMetricGroup(id);
    }

    @PostMapping
    @Operation(description = "Tạo mới SLA Metric")
    public ResponseEntity<SlaMetricResponseDTO> createSlaMetric(@Valid @RequestBody SlaMetricCreateRequestDTO request) {
        log.info("createSlaMetric: start with request {}", request);
        SlaMetricResponseDTO response = slaMetricService.createSlaMetric(request);
        log.info("createSlaMetric: end with id {}", response.getId());
        return ResponseEntity.ok(response);
    }

    @PutMapping
    @Operation(description = "Cập nhật SLA Metric")
    public ResponseEntity<SlaMetricResponseDTO> updateSlaMetric(@Valid @RequestBody SlaMetricUpdateRequestDTO request) {
        log.info("updateSlaMetric: start with request {}", request);
        SlaMetricResponseDTO response = slaMetricService.updateSlaMetric(request);
        log.info("updateSlaMetric: end with id {}", response.getId());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "Xóa SLA Metric")
    public ResponseEntity<Void> deleteSlaMetric(@PathVariable Long id) {
        log.info("deleteSlaMetric: start with id {}", id);
        slaMetricService.deleteSlaMetric(id);
        log.info("deleteSlaMetric: end");
        return ResponseEntity.ok().build();
    }

    @DeleteMapping
    @Operation(description = "Xóa nhiều SLA Metric")
    public ResponseEntity<SlaMetricDeleteResDTO> deleteSlaMetrics(
            @Valid @RequestBody SlaMetricDeleteReqDTO request) {
        log.info("deleteSlaMetrics: start with {} ids", request.getIds().size());
        SlaMetricDeleteResDTO response = slaMetricService.deleteSlaMetrics(request);
        log.info("deleteSlaMetrics: end successfully");
        return ResponseEntity.ok(response);
    }

    @PostMapping("/validate-and-generate-name")
    @Operation(description = "Validate label và generate name cho SLA Metric")
    public ResponseEntity<SlaMetricNameResponseDTO> validateAndGenerateName(@Valid @RequestBody SlaMetricValidateRequestDTO request) {
        log.info("validateAndGenerateName: start with label {}", request.getLabel());
        SlaMetricNameResponseDTO response = slaMetricService.validateAndGenerateName(request);
        log.info("validateAndGenerateName: end with code {}", response.getSystemName());
        return ResponseEntity.ok(response);
    }


    @Operation(description = "Lấy danh sách chỉ số & mục tiêu có phân trang và lọc")
    @GetMapping
    public Page<IGetListMetric> getListMetrics(
            @RequestParam(value = "value",required = false, defaultValue = "") String value,
            @RequestParam(value = "isMetricName",required = false, defaultValue = "0") Integer isMetricName,
            @RequestParam(value = "isMetricCode",required = false, defaultValue = "0") Integer isMetricCode,
            @RequestParam(value = "metricGroup",required = false, defaultValue = "-1") List<Long> metricGroup,
            @RequestParam(value = "dataSource",required = false, defaultValue = "") List<SlaObjectTypeEnum> dataSource,
            @RequestParam(value = "metricType",required = false, defaultValue = "") List<SlaMetricTypeEnum> metricType,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "startDate", required = false, defaultValue = "01/01/1970") Date startDate,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "endDate", required = false, defaultValue = "01/01/3000") Date endDate,
            @RequestParam(value = "page",required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size",required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort",required = false, defaultValue = "createdAt,desc") String sort){
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return slaMetricService.getListMetric(isMetricName, isMetricCode, value, metricGroup, dataSource, metricType, startDate, endDate, listRequest.getPageable());
    }

    @Operation(description = "Lấy danh sách droplist nhóm chỉ số")
    @GetMapping("/metrics-group")
    public List<ICommonIdName> getListMetricGroup(){
        return slaMetricService.getListMetricGroup();
    }


}
