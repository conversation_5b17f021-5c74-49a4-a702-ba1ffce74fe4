package com.service.utils.condition.impl.workOrders;

import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.jsonObject.McIfConditionDTO;

public class WorkOrderTypeCondItem extends CondItemBase {

    public WorkOrderTypeCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
    }

    @Override
    public String getCondition(Integer objectType) {
        String condition = super.getConditionByFixStringColumn("name");
        return "wo_orders.id IN (SELECT wo_orders.id FROM vnpt_dev.wo_orders WHERE type_id IN  (" +
                "	select id from vnpt_dev.wo_types where " + condition +
                "))";
    }
}
