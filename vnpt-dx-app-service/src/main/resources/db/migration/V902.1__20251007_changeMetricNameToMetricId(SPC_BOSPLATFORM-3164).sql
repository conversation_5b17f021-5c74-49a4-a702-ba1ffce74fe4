-- Thay đổi metric_name thành metric_id trong các bảng con SLA Metric

-- 1. Thê<PERSON> cột metric_id vào bảng sla_metric_time_based
ALTER TABLE sla_metric_time_based 
ADD COLUMN metric_id BIGINT;

-- 2. <PERSON>h<PERSON><PERSON> cột metric_id vào bảng sla_metric_ratio_based  
ALTER TABLE sla_metric_ratio_based
ADD COLUMN metric_id BIGINT;

-- 3. Thê<PERSON> cột metric_id vào bảng sla_metric_value_based
ALTER TABLE sla_metric_value_based
ADD COLUMN metric_id BIGINT;

-- 4. <PERSON><PERSON><PERSON> nh<PERSON><PERSON> dữ liệu: map metric_name sang metric_id
-- <PERSON><PERSON><PERSON> nh<PERSON>t sla_metric_time_based
UPDATE sla_metric_time_based 
SET metric_id = (
    SELECT id FROM sla_metrics 
    WHERE sla_metrics.name = sla_metric_time_based.metric_name
);

-- <PERSON><PERSON><PERSON> nh<PERSON>t sla_metric_ratio_based
UPDATE sla_metric_ratio_based 
SET metric_id = (
    SELECT id FROM sla_metrics 
    WHERE sla_metrics.name = sla_metric_ratio_based.metric_name
);

-- <PERSON><PERSON><PERSON> nhật sla_metric_value_based
UPDATE sla_metric_value_based 
SET metric_id = (
    SELECT id FROM sla_metrics 
    WHERE sla_metrics.name = sla_metric_value_based.metric_name
);

-- 5. Thêm NOT NULL constraint cho metric_id
ALTER TABLE sla_metric_time_based 
ALTER COLUMN metric_id SET NOT NULL;

ALTER TABLE sla_metric_ratio_based
ALTER COLUMN metric_id SET NOT NULL;

ALTER TABLE sla_metric_value_based
ALTER COLUMN metric_id SET NOT NULL;

-- 6. Thêm foreign key constraints
ALTER TABLE sla_metric_time_based
ADD CONSTRAINT fk_sla_metric_time_based_metric_id 
FOREIGN KEY (metric_id) REFERENCES sla_metrics(id) ON DELETE CASCADE;

ALTER TABLE sla_metric_ratio_based
ADD CONSTRAINT fk_sla_metric_ratio_based_metric_id 
FOREIGN KEY (metric_id) REFERENCES sla_metrics(id) ON DELETE CASCADE;

ALTER TABLE sla_metric_value_based
ADD CONSTRAINT fk_sla_metric_value_based_metric_id 
FOREIGN KEY (metric_id) REFERENCES sla_metrics(id) ON DELETE CASCADE;

-- 7. Tạo indexes cho performance
CREATE INDEX idx_sla_metric_time_based_metric_id ON sla_metric_time_based(metric_id);
CREATE INDEX idx_sla_metric_ratio_based_metric_id ON sla_metric_ratio_based(metric_id);
CREATE INDEX idx_sla_metric_value_based_metric_id ON sla_metric_value_based(metric_id);

-- 8. Xóa cột metric_name cũ
ALTER TABLE sla_metric_time_based DROP COLUMN metric_name;
ALTER TABLE sla_metric_ratio_based DROP COLUMN metric_name;
ALTER TABLE sla_metric_value_based DROP COLUMN metric_name;

-- 9. Thêm comments
COMMENT ON COLUMN sla_metric_time_based.metric_id IS 'ID metric (tham chiếu đến sla_metrics.id)';
COMMENT ON COLUMN sla_metric_ratio_based.metric_id IS 'ID metric (tham chiếu đến sla_metrics.id)';
COMMENT ON COLUMN sla_metric_value_based.metric_id IS 'ID metric (tham chiếu đến sla_metrics.id)';
