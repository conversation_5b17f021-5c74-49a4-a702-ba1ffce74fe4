# Cập nhật SLA Template Threshold từ Single Object sang List

## Tổng quan thay đổi

Bảng `SlaTemplateMetric` đã được cập nhật để hỗ trợ nhiều ngưỡng (threshold) cho mỗi loại thay vì chỉ một ngưỡng duy nhất. Điều này cho phép định nghĩa các điều kiện phức tạp hơn cho SLA.

## Các thay đổi chính

### 1. Entity Changes
- **SlaTemplateMetric**: <PERSON><PERSON><PERSON> trường `targetThreshold`, `warningThreshold`, `breachThreshold` đã được thay đổi từ `SlaMetricThresholdDTO` thành `List<SlaMetricThresholdDTO>`

### 2. DTO Changes
- **SlaTemplateMetricConfigDTO**: Cập nhật các trường threshold thành List
- **SlaTemplateResponseDTO.SlaTemplateMetricResponseDTO**: Cập nhật response để trả về List threshold

### 3. Service Layer Changes
- **SlaTemplateServiceImpl**: 
  - Thêm method `convertThresholdListToResponse()` để convert List threshold
  - Cập nhật logic xử lý trong `convertToResponseDTO()`

## Cấu trúc dữ liệu mới

### Trước đây (Single Threshold)
```json
{
  "targetThreshold": {
    "operator": "LESS_THAN",
    "value": 60.0,
    "unit": "minutes"
  }
}
```

### Hiện tại (List Threshold)
```json
{
  "targetThreshold": [
    {
      "operator": "LESS_THAN",
      "value": 60.0,
      "unit": "minutes"
    },
    {
      "operator": "GREATER_THAN",
      "value": 0.0,
      "unit": "minutes"
    }
  ]
}
```

## Ưu điểm của thay đổi

1. **Linh hoạt hơn**: Có thể định nghĩa nhiều điều kiện cho một ngưỡng
2. **Phức tạp hơn**: Hỗ trợ các logic AND/OR giữa các điều kiện
3. **Mở rộng tương lai**: Dễ dàng thêm các loại điều kiện mới

## Ví dụ sử dụng

### Ví dụ 1: Ngưỡng mục tiêu với khoảng giá trị
```json
{
  "targetThreshold": [
    {
      "operator": "GREATER_THAN_OR_EQUAL",
      "value": 30.0,
      "unit": "minutes"
    },
    {
      "operator": "LESS_THAN_OR_EQUAL",
      "value": 60.0,
      "unit": "minutes"
    }
  ]
}
```
Ý nghĩa: Thời gian phản hồi phải từ 30-60 phút

### Ví dụ 2: Ngưỡng cảnh báo với nhiều điều kiện
```json
{
  "warningThreshold": [
    {
      "operator": "GREATER_THAN",
      "value": 80.0,
      "unit": "percent"
    },
    {
      "operator": "LESS_THAN",
      "value": 95.0,
      "unit": "percent"
    }
  ]
}
```
Ý nghĩa: Cảnh báo khi tỷ lệ thành công từ 80-95%

## Migration Notes

### Dữ liệu cũ
- Dữ liệu cũ với single threshold vẫn tương thích
- Hệ thống sẽ tự động convert single object thành array có 1 phần tử

### API Compatibility
- API request/response format đã thay đổi
- Client cần cập nhật để gửi array thay vì single object
- Response sẽ trả về array thay vì single object

## Testing

Đã tạo test case trong `SlaTemplateServiceImplTest` để kiểm tra:
- Tạo SLA template với List threshold
- Convert threshold list sang response DTO
- Xử lý null/empty list

## Files đã thay đổi

1. `SlaTemplateMetric.java` - Entity
2. `SlaTemplateMetricConfigDTO.java` - Request DTO
3. `SlaTemplateResponseDTO.java` - Response DTO
4. `SlaTemplateServiceImpl.java` - Service logic
5. `SlaTemplateServiceImplTest.java` - Test cases (mới)
6. `sla-template-request-example.json` - Ví dụ request (mới)

## Lưu ý quan trọng

1. **Backward Compatibility**: Cần kiểm tra và cập nhật client code
2. **Database**: Cấu trúc JSONB trong database không thay đổi
3. **Validation**: Cần thêm validation cho List threshold nếu cần
4. **Performance**: Cần monitor performance khi xử lý nhiều threshold

## Tương lai

Có thể mở rộng thêm:
- Logic operator giữa các threshold (AND/OR)
- Grouping threshold theo priority
- Conditional threshold dựa trên context
