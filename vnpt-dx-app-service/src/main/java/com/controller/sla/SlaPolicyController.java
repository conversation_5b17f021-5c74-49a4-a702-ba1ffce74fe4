package com.controller.sla;

import com.component.BaseController;
import com.dto.sla.IGetListPolicies;
import com.onedx.common.constants.enums.Status;
import com.onedx.common.utils.DateUtil;
import com.service.sla.SlaPolicyService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import com.dto.common.ICommonIdName;
import com.dto.sla.SlaPolicyCreateDTO;
import com.service.sla.SlaTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/admin-portal/sla-policy")
@Slf4j
@Validated
@RequiredArgsConstructor
public class SlaPolicyController {

    private final SlaPolicyService slaPolicyService;
    private final SlaTemplateService slaTemplateService;

    @Operation(description = "Lấy danh sách chỉ số & mục tiêu có phân trang và lọc")
    @GetMapping
    public Page<IGetListPolicies> getListPolicies(
            @RequestParam(value = "value", required = false, defaultValue = "") String value,
            @RequestParam(value = "isPoliciesName", required = false, defaultValue = "0") Integer isPoliciesName,
            @RequestParam(value = "status", required = false, defaultValue = "UNSET") List<Status> status,
            @RequestParam(value = "createdBy", required = false, defaultValue = "-1") Long createdBy,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "startDate", required = false, defaultValue = "01/01/1970") Date startDate,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "endDate", required = false, defaultValue = "01/01/3000") Date endDate,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "createdAt,desc") String sort) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return slaPolicyService.getListPolicies(isPoliciesName, value, status, createdBy, startDate, endDate, listRequest.getPageable());
    }

    @Operation(description = "Xóa chính sách")
    @DeleteMapping("/{policiesId}")
    public void deletePolicies(@PathVariable("policiesId") Long policiesId) {
        slaPolicyService.deletePolicies(policiesId);
    }

    @PostMapping
    @Operation(description = "Tạo chính sách SLA")
    public Long createSlaPolicy(@RequestBody SlaPolicyCreateDTO dto) {
        return slaPolicyService.createSlaPolicy(dto);
    }

    @PutMapping("/{id}")
    @Operation(description = "Cập nhật chính sách SLA")
    public Long updateSlaPolicy(@RequestBody SlaPolicyCreateDTO dto, @PathVariable Long id) {
        return slaPolicyService.updateSlaPolicy(dto, id);
    }

    @GetMapping("/validate-name")
    @Operation(description = "Validate tên chính sách")
    public void validateName(
            @RequestParam(name = "name") String name,
            @RequestParam(name = "id", required = false, defaultValue = "-1") Long id
    ) {
        slaPolicyService.validateName(name, id);
    }

    @GetMapping("/templates")
    @Operation(description = "Danh sách mẫu template")
    public List<ICommonIdName> getSlaTemplates(
            @RequestParam(name = "name", required = false, defaultValue = "") String name
    ) {
        return slaTemplateService.getSlaTemplates(name);
    }
}
