package com.dto.sla;

import com.onedx.common.constants.enums.StatusEnum;

public interface IGetSlaMetricGroupDTO {
    Long getId();
    String getName();        // Tên nhóm chỉ số
    String getDescription(); // <PERSON>ô tả nhóm chỉ số
    String getCreatedBy();   // Ng<PERSON><PERSON><PERSON> tạo (hiển thị)
    String getStatusInt();
    
    default StatusEnum getStatus() {
        return StatusEnum.valueOf(getStatusInt());
    }

}
