package com.service.sla.impl;

import com.dto.sla.IGetSlaAttributeGroupDTO;
import com.dto.sla.SlaAttributeGroupDTO;
import com.dto.sla.SlaAttributeUpdateDTO;
import com.entity.sla.SlaAttributes;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.exception.ExceptionFactory;
import com.repository.sla.SlaAttributeRepository;
import com.service.sla.SlaAttributesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SlaAttributesServiceImpl implements SlaAttributesService {

    private final SlaAttributeRepository slaAttributeRepository;
    private final ExceptionFactory exceptionFactory;

    /**
     * <PERSON><PERSON>y danh sách thuộc tính theo nguồn dữ liệu
     */
    @Override
    public List<SlaAttributeGroupDTO> getGroupedAttributes() {
        List<IGetSlaAttributeGroupDTO> listAttributes = slaAttributeRepository.getGroupedAttributes();
        return listAttributes.stream().map(SlaAttributeGroupDTO::new).collect(Collectors.toList());
    }

    /**
     * Cập nhật thuộc tính SLA
     */
    @Override
    @Transactional
    public void updateSlaAttributes(Long id, SlaAttributeUpdateDTO request) {
        SlaAttributes slaAttributes = slaAttributeRepository.findById(id)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SLA_ATTRIBUTES, ErrorKey.ID, String.valueOf(id)));

        slaAttributes.setStatus(request.getStatus());
        slaAttributes.setLabel(request.getLabel());
        slaAttributeRepository.save(slaAttributes);
    }
}
