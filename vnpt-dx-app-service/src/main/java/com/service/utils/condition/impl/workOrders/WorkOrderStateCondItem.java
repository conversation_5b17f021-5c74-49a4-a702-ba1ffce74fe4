package com.service.utils.condition.impl.workOrders;

import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.jsonObject.McIfConditionDTO;

public class WorkOrderStateCondItem extends CondItemBase {

    public WorkOrderStateCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
    }

    @Override
    public String getCondition(Integer objectType) {
        String condition = super.getConditionByFixStringColumn("name");
        return "wo_orders.id IN (SELECT wo_orders.id FROM vnpt_dev.wo_orders WHERE state_id IN  (\n" +
                "	select id from vnpt_dev.states where classification = 'WORK_ORDER' and \n" + condition +
                "))";
    }
}
