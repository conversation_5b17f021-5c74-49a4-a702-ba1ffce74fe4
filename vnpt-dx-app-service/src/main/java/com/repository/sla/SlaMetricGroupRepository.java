package com.repository.sla;

import com.constant.sql.sla.SQLSla;
import com.dto.common.ICommonIdName;
import com.dto.sla.IGetSlaMetricGroupDTO;
import com.entity.sla.SlaMetricGroup;
import com.onedx.common.constants.enums.StatusEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import java.util.Collection;
import java.util.List;

@Repository
public interface SlaMetricGroupRepository extends JpaRepository<SlaMetricGroup, Long> {
    boolean existsByNameAndDeletedFlagAndIdNot(String name, Integer deletedFlag, Long id);

    @Query(nativeQuery = true, value = SQLSla.GET_LIST_METRIC_GROUP)
    Page<IGetSlaMetricGroupDTO> getMetricGroup(Integer isName, String value, int status, Long createdBy, Pageable pageable);

    SlaMetricGroup findByIdAndDeletedFlag(Long id, int value);

    @Query(nativeQuery = true, value = SQLSla.GET_LIST_CREATED_BY_METRIC_GROUP)
    List<ICommonIdName> getListCreatedByMetricGroup(String name);
}
