BEGIN;

-- T<PERSON><PERSON> bảng tạm thời để lưu state_transition_id
CREATE TEMP TABLE IF NOT EXISTS temp_transition_id AS
SELECT id FROM vnpt_dev.state_transitions
WHERE object_type = 'PHYSICAL' AND name = '<PERSON><PERSON><PERSON><PERSON> đổi trạng thái hàng hóa vật lý'
    LIMIT 1;

-- Tạ<PERSON> bảng tạm thời để lưu state_ids
CREATE TEMP TABLE IF NOT EXISTS temp_state_ids AS
SELECT
    name,
    id,
    code
FROM vnpt_dev.states
WHERE object_type = 'PHYSICAL'
  AND code IN (
               'TT000001', 'TT000002', 'TT000003', 'TT000004', 'TT000005',
               'TT000006', 'TT000007', 'TT000008', 'TT000009', 'TT000010', 'TT000011'
    );

-- Xóa state_transition_items cũ nếu có
DELETE FROM vnpt_dev.state_transition_item_triggers
WHERE state_transition_item_id IN (
    SELECT id FROM vnpt_dev.state_transition_items
    WHERE state_transition_id = (SELECT id FROM temp_transition_id)
);

DELETE FROM vnpt_dev.state_transition_items
WHERE state_transition_id = (SELECT id FROM temp_transition_id);

-- Insert dữ liệu cho trạng thái requested_warehouse - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'requested_warehouse';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           NULL,
           '[{
               "api": null,
               "type": "RULE_ENGINE",
               "manual": null,
               "webhook": null,
               "schedule": null,
               "ruleEngine": {
                   "conditions": [
                       {
                           "id": 1,
                           "key": "1",
                           "ifconds": [
                               {
                                   "id": 1,
                                   "key": "1",
                                   "data": {
                                       "value": [-1]
                                   },
                                   "operator": 1,
                                   "operandId": 5004
                               }
                           ]
                       }
                   ]
               }
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái preparing_stock (từ trạng thái requested_warehouse) - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'preparing_stock';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'requested_warehouse';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái ready_for_shipping - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'ready_for_shipping';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'preparing_stock';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái pending_shipping - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'pending_shipping';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'ready_for_shipping';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái shipped - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'shipped';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'pending_shipping';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái delivered - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'delivered';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'shipped';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái waiting_installation - RULE_ENGINE với hỗ trợ lắp đặt
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'waiting_installation';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'delivered';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "RULE_ENGINE",
               "manual": null,
               "webhook": null,
               "schedule": null,
               "ruleEngine": {
                   "conditions": [
                       {
                           "id": 1,
                           "key": "1",
                           "ifconds": [
                               {
                                   "id": 1,
                                   "key": "1",
                                   "data": {
                                       "value": ["SUPPORT"]
                                   },
                                   "operator": 1,
                                   "operandId": 1056
                               }
                           ]
                       }
                   ]
               }
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái in_installation - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'in_installation';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'waiting_installation';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái installed - MANUAL
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'installed';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'in_installation';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái online_verified - MANUAL (từ installed)
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'online_verified';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'installed';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái cancelled - MANUAL (chỉ từ online_verified)
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'cancelled';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'online_verified';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Insert dữ liệu cho trạng thái online_verified từ delivered - MANUAL TH không có lắp đặt
DO $$
DECLARE
v_transition_id bigint;
    v_state_id bigint;
    v_pre_state_id bigint;
    v_item_id bigint;
BEGIN
    -- Lấy các ID cần thiết
SELECT id INTO v_transition_id FROM temp_transition_id;
SELECT id INTO v_state_id FROM temp_state_ids WHERE name = 'online_verified';
SELECT id INTO v_pre_state_id FROM temp_state_ids WHERE name = 'delivered';

-- Insert state_transition_item (không có post_actions)
INSERT INTO vnpt_dev.state_transition_items (
    state_transition_id,
    state_id
)
VALUES (
           v_transition_id,
           v_state_id
       )
    RETURNING id INTO v_item_id;

-- Insert state_transition_item_triggers với post_actions
INSERT INTO vnpt_dev.state_transition_item_triggers (
    state_transition_item_id,
    pre_state_id,
    triggers,
    post_actions
)
VALUES (
           v_item_id,
           v_pre_state_id,
           '[{
               "api": null,
               "type": "MANUAL",
               "manual": {
                   "roles": ["FULL_ADMIN"],
                   "agentTypes": ["ADMIN"]
               },
               "webhook": null,
               "schedule": null,
               "ruleEngine": null
           }]'::jsonb,
           jsonb_build_object(
                   'roles', jsonb_build_array('FULL_ADMIN'),
                   'notifications', jsonb_build_array(jsonb_build_object('type', 'NOTIFICATION'))
           )
       );
END $$;

-- Xóa bảng tạm thời khi hoàn thành
DROP TABLE temp_transition_id;
DROP TABLE temp_state_ids;

COMMIT;
