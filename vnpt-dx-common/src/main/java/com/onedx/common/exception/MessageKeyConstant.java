package com.onedx.common.exception;

import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MessageKeyConstant {

    public static final String VNPTPAY_UNKNOWN = "error.vnptpay.unknown";
    public static final String VNPTPAY_INIT = "error.vnptpay.init";
    public static final String VNPTPAY_PAYMENT = "error.vnptpay.payment";
    public static final String EXISTS = "exists";
    public static final String EXISTS_EMAIL_OTHER_CUSTOMER_TYPE = "exists.email.other.customer.type";
    public static final String SECURE_CODE_INVALID = "error.secure.code.invalid";
    public static final String SUBSCRIPTION_CODE_EXIST = "error.subscription.code.exist";
    public static final String ITEM_MISMATCH = "order.item.mismatch";
    public static final String AFF_MAX = "error.aff.max";
    public static final String AFF_MEMBER_CAN_NOT_APPLY = "error.affiliate.member.can.not.apply";
    public static final String AFF_CODE_NULL = "error.aff.code.null";
    public static final String VARIANT_CODE_NULL = "error.variant.code.null";
    public static final String SUBSCRIPTION_CODE_NOT_VALID = "error.not.valid.subscription.code";
    public static final String AFF_PARENT_NOT_APPROVED = "error.aff.parent.not.approved";
    public static final String AFF_PARENT_NOT_ACTIVE = "error.aff.parent.not.active";
    public static final String AFF_NOT_PARENT = "error.aff.not.parent";
    public static final String NOT_FOUND = "error.object.not.found";
    public static final String SHOPPING_CART_CANT_CONTAIN_PHYSICAL_AND_NON_PHYSICAL_PRODUCT = "error.shopping.cart.can.not.contain.physical.and.non.physical.product";

    public static final String CATEGORY_PARENT_IS_FAILED = "error.parent.is.failed";

    public static final String FORBIDDEN = "error.forbbiden.resource";
    public static final String UN_VALID = "error.object.layout.template.dont.delete";
    public static final String AFF_DELETE = "error.aff.delete";
    public static final String AFF_NOT_ACTIVE = "error.aff.not.active";
    public static final String EXIST_AFF_COM_EVENT = "error.exist.aff.com.event";
    public static final String AFF_BILL_TIME_IS_NULL = "error.aff.bill.time.null";
    public static final String AFF_BILL_IS_NULL = "error.aff.bill.null";
    public static final String WEEK_PASSWORD = "error.weak.password";
    public static final String INVALID_FIELD = "error.invalid.field";

    public static final String ACTIVE_LAYOUT = "error.object.layout.template.can.not.disable";
    public static final String ACTIVITY_LOG_DETAIL = "error.cannot.update.because.activity.log.processed";
    public static final String API_URL_NOT_EDIT = "error.cannot.update.api.url";
    public static final String API_URL_NOT_NULL = "error.api.url.not.null";
    public static final String PRICING_NOT_FOUND = "error.pricing.not.found";
    public static final String PRICING_OFFICIAL_NOT_FOUND = "error.pricing.official.not.found";
    public static final String USER_NOT_FOUND = "error.user.not.found";
    public static final String COMBO_PLAN_NOT_FOUND = "error.combo.plan.not.found";
    public static final String REQUIRED_PRICING_MULTI_PLAN = "error.pricing.multi.plan.id.must.require";
    public static final String DISABLED_PRICING_MULTI_PLAN = "error.pricing.multi.plan.id.disabled";
    public static final String SERVICE_DISABLED_MULTI_SUB = "error.service.disable.multi.sub";
    public static final String SERVICE_GROUP_DISABLED_MULTI_SUB = "error.service.group.disable.multi.sub";
    public static final String SERVICE_GROUP_UNAVAILABLE_DEVICE_PRODUCT = "error.service.group.unavailable.device.product";
    public static final String SERVICE_GROUP_CUSTOMER_TYPE_NOT_MATCH = "error.service.group.customer.type.not.match";
    public static final String COMBO_DISABLED_MULTI_SUB = "error.combo.disable.multi.sub";
    public static final String INVALID_PRICING_MULTI_PLAN_ID = "error.pricing.multi.plan.id.invalid";
    public static final String REQUIRED_ADDON_MULTI_PLAN = "error.addon.multi.plan.id.must.require";
    public static final String ADDON_MINIMUM_QUANTITY = "error.addon.minimum.quantity.invalid";
    public static final String ADDON_MAXIMUM_QUANTITY = "error.addon.maximum.quantity.invalid";
    public static final String ADDON_INVALID_QUANTITY = "error.addon.quantity.invalid";
    public static final String PRICING_MULTI_PLAN_MINIMUM_QUANTITY = "error.pricing.multi.plan.minimum.quantity.invalid";
    public static final String PRICING_MULTI_PLAN_MAXIMUM_QUANTITY = "error.pricing.multi.plan.maximum.quantity.invalid";
    public static final String PRICING_MULTI_PLAN_INVALID_QUANTITY = "error.pricing.multi.plan.quantity.invalid";
    public static final String UNIT_INVALID = "error.unit.invalid";
    public static final String CURRENCY_INVALID = "error.currency.invalid";
    public static final String INVALID_COUPON_QUANTITY = "error.coupon.quantity.invalid";

    public static final String NO_HAVE_ACCESS = "error.no.have.access";
    public static final String NOT_EXIST_VARIANT = "error.not.exist.variant";
    public static final String NOT_EXIST_SERVICE = "error.not.exist.service";
    public static final String SERVICE_NOT_ACTIVE = "error.service.not.active";
    public static final String SERVICE_NOT_CUSTOMER_IN_SERVICE_GROUP = "error.service.not.customer.in.service.group";
    public static final String NO_HAVE_ACCESS_DHSXKD = "error.connect.to.api.dhsxkd";
    public static final String HAVE_ERROR_TRACKING_FROM_DHSXKD = "error.no.data.because.have.exception.in.api.tracking.dhsxkd";
    public static final String PERMISSION_DENIED = "error.permission.denied";

    public static final String INVALID_SERVICE_APPROVE_CONFIG = "error.service.approve.config.invalid";

    public static final String YOU_USED_TRY = "error.you.used.pricing.try";
    public static final String YOU_USED = "error.you.used.pricing";
    public static final String PAYMENT_STATUS_SUCCESS = "payment.status.success";
    public static final String PAYMENT_NOT_CHANGE = "error.payment.can.not.change";
    public static final String FIELD_MUST_BE_NOT_NULL = "error.field.must.be.not.null";
    public static final String SUBSCRIPTION_IS_CANCEL = "error.subscription.is.cancel";
    public static final String DUPLICATE_NAME = "error.duplicate.name";
    public static final String DUPLICATE_SKU = "error.duplicate.sku";
    public static final String SKU_IS_NULL = "error.default.sku.not.null";
    public static final String INVALID_TRANSITION = "error.invalid.transition";

    public static final String EMPLOYEE_DELETED = "error.employee.deleted";
    public static final String EMPLOYEE_INACTIVE = "error.employee.inactive";
    public static final String DUPLICATE_IDENTITY_NO = "error.duplicate.identityNo";
    public static final String DUPLICATE_SLUG_NAME = "error.duplicate.slug.name";
    public static final String DUPLICATE_CODE = "error.duplicate.code";
    public static final String FIELD_WRONG_FORMAT = "error.field.wrong.format";
    public static final String FIELD_DATA_INVALID = "error.field.invalid";
    public static final String OBJECT_USED_IN_ANOTHER_OBJECT = "error.object.being.used.in.another.object";
    public static final String CAN_NOT_DELETE_HAS_CHILD = "error.can.not.delete.has.child";
    public static final String CAN_NOT_DELETE_ASSOCIATION = "error.can.not.delete.association";
    public static final String INVALID_FILE = "error.invalid.file";
    public static final String WRONG_TYPE = "error.wrong.type";
    public static final String MANDATORY_WRONG_TYPE = "error.mandatory.wrong.type";
    public static final String FIELD_TOO_LONG = "error.field.too.long";
    public static final String UNSUPPORTED = "error.unsupported";
    public static final String IO = "error.io";
    public static final String FIELD_MUST_BE_NULL = "error.field.must.be.null";
    public static final String INVALID_STATUS = "error.invalid.status";
    public static final String CATEGORY_WRONG_TYPE = "error.category.wrong.type";
    public static final String TICKET_HAS_BEEN_ASSIGNED = "error.ticket.has.been.assigned";
    public static final String TICKET_HAS_BEEN_RESOLVED = "error.ticket.has.been.resolved";
    public static final String INVALID_OBJECT_TYPE = "error.ticket.invalid.object.type";
    public static final String TICKET_MUST_BE_OPEN = "error.ticket.must.be.open";
    public static final String TICKET_MUST_NOT_BE_RESOLVED = "error.ticket.must.not.be.resolved";
    public static final String TICKET_MUST_BE_IN_PROGRESS = "error.ticket.must.be.in.progress";
    public static final String CURRENT_USER_NOT_BE_SUPPORTER = "error.ticket.user.not.be.supporter";
    public static final String CURRENT_USER_NOT_SAME_COMPANY = "error.current.user.not.same.company";
    public static final String NOT_BE_OWNER_OF_RESPONSE = "error.ticket.response.not.be.owner";
    public static final String SME_NOT_BE_OWNER = "error.ticket.sme.not.be.owner";
    public static final String STATUS_NOT_OPEN = "error.ticket.status.not.open";
    public static final String SUPPORTER_NOT_FOUND = "error.ticket.supporter.not.found";
    public static final String MUST_BE_GREATER_THAN_0 = "error.greater.than.0";
    public static final String MUST_BE_LESS_THAN_ST = "error.less.than.st";
    public static final String ST_MUST_BE_GREATER_THAN_ST = "error.st.greater.than.st";
    public static final String SERVICE_IS_NOT_OWNED = "error.service.is.not.owned";
    public static final String SERVICE_IS_NOT_FOUND = "error.service.is.not.found";
    public static final String SERVICE_APPROVE_STATUS_CAN_NOT_CHANGE = "error.service.approve.status.can.not.change";
    public static final String SERVICE_LANGUAGE_NOT_MATCH = "error.service.language.not.match";
    public static final String SERVICE_PRIORITY_CONSECUTIVE = "error.service.priority.not.consecutive";
    public static final String INVALID_AUTHENTICATION = "error.invalid.authentication";
    public static final String NOTIFICATION_NOT_FOUND = "error.notfound.notification";
    public static final String USER_NOT_OWN_NOTIFICATION = "error.user.not.own.notification";
    public static final String PORTAL_NOT_SUPPORT = "error.portal.not.support";
    public static final String SERVICE_STATUS_CAN_NOT_CHANGE = "error.service.status.can.not.change";
    public static final String SUBSCRIPTION_PLAN_ARE_TOO_QUANTITY = "error.subscription.plan.are.too.quantity";
    public static final String QUARTER_INVALID = "error.invalid.quarter";
    public static final String USER_ACCESS_DENIED = "error.service.user.access.denied";
    public static final String MASOFFER_TOKEN_INVALID = "error.masoffer.authorization.invalid";
    public static final String MASOFFER_DATA_INVALID = "error.masoffer.data.invalid";
    public static final String INVALID_SUBSCRIPTION_PLAN = "error.invalid.subscription.plan";
    public static final String INVALID_RECOMMENDED_STATUS = "error.invalid.recommended.status";
    public static final String INVALID_SORT_ORDER = "error.invalid.sort.order";
    public static final String USER_NOT_OWN_DEPARTMENT = "error.department.user.not.own";
    public static final String NOT_IN_SAME_SME = "error.department.not.in.same.sme";
    public static final String DEPARTMENT_ACTIVE = "error.department.active";
    public static final String INACTIVE_DEPARTMENT_CAN_NOT_CREATE = "error.inactive.department.can.not.create";
    public static final String DEFAULT_VARIANT_IS_NULL = "error.default.status.not.null";
    public static final String DEFAULT_VARIANT_IS_MULTI = "error.default.status.is.multi";
    public static final String ACTIVE_DEPARTMENT_CAN_NOT_CREATE = "error.active.department.can.not.create";
    public static final String DUPLICATE_NAME_BY_PARENT = "error.duplicate.name.by.parent";
    public static final String DEPARTMENT_HAS_CHILD = "error.department.has.child";
    public static final String PARENT_DEPARTMENT_NOT_FOUND = "error.parent.department.not.found";
    public static final String PARENT_DEPARTMENT_INACTIVE = "error.parent.department.inactive";
    public static final String CANNOT_SAVE_WITH_INACTIVE_PARENT = "error.department.can.not.save";
    public static final String DEPARTMENT_ACTIVE_EMPLOYEE = "error.department.active.employee";
    public static final String DEPARTMENT_ACTIVE_SUB_DEPARTMENT = "error.department.active.sub.department";
    public static final String PRICING_NOT_BE_DRAFT = "error.pricing.not.be.draft";
    public static final String PRICING_NOT_BE_AWAITING_APPROVAL = "error.pricing.not.be.awaiting.approval";
    public static final String PRICING_RECOMMENDATION_LIMIT = "error.pricing.recommendation.limit";
    public static final String PRICING_DEFAULT_LIMIT = "error.pricing.default.limit";
    public static final String COUPON_IS_VAT_INVALID = "error.coupon.isVat";
    public static final String COUPON_CODE_TYPE = "error.coupon.code.type";
    public static final String COUPON_DISCOUNT_CODE_TYPE = "error.coupon.discount.type";
    public static final String COUPON_TIMES_USES_TYPE = "error.coupon.timesUsed.type";
    public static final String COUPON_ENTERPRISE_TYPE = "error.coupon.enterprise.type";
    public static final String COUPON_PRICING_TYPE = "error.coupon.pricing.type";
    public static final String COUPON_ADDON_TYPE = "error.coupon.addon.type";
    public static final String COUPON_TYPE = "error.coupon.type";
    public static final String COUPON_PROMOTION_TYPE = "error.promotion.type";
    public static final String PRICING_NEED_AWAITING_APPROVAL = "error.pricing.not.awaiting.approval";

    public static final String COUPON_NEED_APPROVE = "error.object.need.approve";
    public static final String COUPON_HAS_BEEN_USED = "error.coupon.has.been.used";
    public static final String USER_NOT_OWN_COUPON = "error.coupon.user.not.own";
    public static final String USER_NOT_OWN_ADDONS = "error.addon.user.not.own";
    public static final String COUPON_EXPIRED = "error.coupon.invalid.expired";

    public static final String USER_IS_NOT_MANAGED_BY_YOU = "error.user.is.not.managed.by.you";
    public static final String INVALID_DATA = "error.invalid.data";
    public static final String APPLY_CONDITION_NOT_MATCH = "error.apply.condition.not.match";

    public static final String INVALID_CONFIGURATION = "error.invalid.configuration";
    public static final String DATA_OUT_OF_RANGE = "error.data.out.of.range";
    public static final String SERVICE_CODE_INVALID_DATA = "error.invalid.data";
    public static final String SERVICE_TYPE_INVALID_DATA = "error.invalid.data";
    public static final String SERVICE_HAS_BEEN_REGISTERED = "error.service.has.been.registered";
    public static final String SUBSCRIPTION_CANNOT_DECREASE_QUANTITY = "error.subscription.cannot.decrease.quantity";
    public static final String SUBSCRIPTION_CANNOT_CHANGE_QUANTITY = "error.subscription.cannot.change.quantity";
    public static final String SUBSCRIPTION_CANNOT_INCREASE_QUANTITY = "error.subscription.cannot.increase.quantity";

    public static final String MUST_BE_NUMBER = "error.must.be.number";
    public static final String SEND_MAIL_ERROR = "error.send.mail.fail";
    public static final String SEND_MAIL_ERROR_INVALID_ADDRESS = "error.send.mail.fail.invalid.address";
    public static final String SETUP_SEND_MAIL_ERROR = "error.setup.send.mail.fail";

    public static final String OBJECT_INVALID_PARAM = "error.object.invalid.param";
    public static final String OBJECT_NOT_FOUND_PARAM = "error.object.not.found.param";

    public static final String INVALID_FILE_LINK = "error.invalid.external.file.link";
    public static final String ERROR_EXPORT_FILE = "error.export.file";
    public static final String MAXIMIM_COMMENT_RESPONSE = "error.maximim.comment.response";
    public static final String NOT_BE_OWNER_OF_EVALUATION_REPLY = "error.rating.comment.reply.not.owner";
    public static final String DUPLICATE = "error.duplicate";
    public static final String DUPLICATE_VALUE = "error.duplicate.value";
    public static final String DUPLICATE_DEFAULT_ITEM = "error.duplicate.item.default";
    public static final String EXCEED_LIMIT_ITEM = "error.exceed.limit.item";

    public static final String DUPLICATE_PRICING = "error.duplicate.pricing";
    public static final String SUBSCRIPTION_CONFLICT = "error.subscription.conflict";
    public static final String DUPLICATE_COMBO = "error.duplicate.combo";
    public static final String DUPLICATE_PRICING_TRY = "error.duplicate.pricing.try";
    public static final String DUPLICATE_SERVICE = "error.duplicate.service";
    public static final String DUPLICATE_SERVICE_TRY = "error.duplicate.service.try";

    public static final String NO_EDITING_PERMISSION = "error.no.editing.permission";

    public static final String PRICING_NOT_TRIED = "error.pricing.not.tried";

    public static final String USER_NOT_USED_SERVICE = "error.object.not.used";
    public static final String CAN_NOT_LIKE_YOUR_OWN_CMT = "error.rating.not.like.your.own.comment";

    public static final String CURRENCY_USED = "error.currency.used";
    public static final String CANT_NOT_DELETE = "error.object.used";
    public static final String DATA_EXISTS = "error.data.exists";

    public static final String DATA_EXISTS_NOT_ACTIVE = "error.data.exists.not.active";
    public static final String EMAIL_EXISTS = "error.email.exists";
    public static final String EMAIL_EXISTS_IN_SYSTEM = "error.email.exists.in.system";
    public static final String PHONE_EXISTS = "error.phone.exists";
    public static final String TIN_EXISTS = "error.tin.exists";
    public static final String REP_PERSONAL_CERT_NUMBER_EXISTS = "error.rep.personal.cert.number.exists";
    public static final String BUSINESS_LICENSE = "error.business.license.number.exists";
    public static final String DATA_NOT_FOUND = "error.data.not.found";
    public static final String MULTISUB_NOT_ALLOWED = "error.multisub.not.allowed";
    public static final String URL_EXISTS = "error.url.exists";
    public static final String PUBLIC_ADDON_GREATER_THAN_MAX_ACCEPTED_VALUE = "error.public.addon.greater.than.max.accepted.value";
    public static final String PLAN_DISPLAY_STATUS_OUT_OF_ACCEPTED_VALUE = "error.public.display-status.outof.accepted.value";
    public static final String PRIVATE_ADDON_GREATER_THAN_MAX_ACCEPTED_VALUE = "error.private.addon.greater.than.max.accepted.value";
    public static final String ADDON_PAYMENT_CYCLE_EXIST = "error.addon.payment.cycle.exists";
    public static final String ADDON_PAYMENT_CYCLE_CAN_NOT_NULL = "error.addon.payment.cycle.can.not.null";
    public static final String END_DATE_MUST_AFTER_START_DATE = "error.end.date.must.after.start.date";
    public static final String LESS_THAN = "error.less.than";
    public static final String CANNOT_CHANGE = "error.not.change";
    public static final String CANNOT_DELETE = "error.object.still.used";
    public static final String CANNOT_DELETE_BECAUSE_REGISTERED = "error.object.still.registered";
    public static final String SUBSCRIPTION_ACTIVATED = "error.subscription.activated";
    public static final String SUBSCRIPTION_CANCELED = "error.subscription.canceled";
    public static final String MISSING_CERTIFICATE_PHOTO = "error.missing.certificate";
    public static final String MISSING_BUSINESS_LICENCE = "error.missing.business.licence";
    public static final String SUBSCRIPTION_IN_TRIAL = "error.subscription.in.trial";
    public static final String USER_IS_ALREADY_AFFILIATE = "error.user.is.already_affiliate";
    public static final String ERROR_USER_ALREADY_ROLE_AFFILIATE = "error.user.already.role.affiliate";

    public static final String FEATURE_STILL_USED = "error.feature.still.used";
    public static final String PRICING_NOT_APPROVED = "error.object.need.approved";
    public static final String PRICING_CUSTOMER_TYPE_NOT_AVAILABLE = "error.customertype.not.available";
    public static final String REQUIRED_UN_APPROVE = "error.object.need.unapprove";

    public static final String USER_NOT_HAVE_PERMISSION = "error.user.not.have.permission";
    public static final String USER_CAN_NOT_DELETE_PRICING = "error.user.can.not.delete";
    public static final String PRICING_STILL_USED = "error.pricing.still.used";
    public static final String COMBO_USING_PRICING = "error.combo.using.pricing";
    public static final String COUPON_APPROVE = "error.pricing.coupon.approve";
    public static final String COUPON_OUT_OF_QUANTITY = "error.coupon.out.of.quantity";
    public static final String SERVICE_OWNER_ORDER_SERVICE = "error.order.service";
    public static final String REQUEST_APPROVE = "error.invalid.approve";
    public static final String APPROVE_NOT_CHANGE = "error.statusApprove.notChange";
    public static final String APPROVE_CAN_NOT_CHANGE = "error.not.approve";
    public static final String USER_NOT_USED_COMBO = "error.object.not.used";

    public static final String DELETED = "error.object.deleted";
    public static final String INACTIVE = "error.object.inactive";
    public static final String ACTIVE = "error.object.active";
    public static final String ITEM_IN_USED = "error.item.in.used";
    public static final String ITEM_IN_REGISTERED = "error.item.in.registered";
    public static final String SERVICE_INVALID = "error.service.invalid";
    public static final String TAX_INVALID = "error.tax.invalid";
    public static final String TAX_INVALID_OVER_RANGE = "error.tax.invalid.over.range";
    public static final String COUPON_PRICING_INVALID = "error.coupon.pricing.invalid";
    public static final String COUPON_PRICING_APPLY_INVALID = "error.coupon.pricing.apply.invalid";
    public static final String COUPON_ENTERPRISE_INVALID = "error.coupon.enterprise.invalid";
    public static final String COUPON_SUPPLIERS_INVALID = "error.coupon.suppliers.invalid";
    public static final String COUPON_ADDONS_INVALID = "error.coupon.addons.invalid";
    public static final String PRICING_ADDONS_INVALID = "error.pricing.addons.invalid";
    public static final String ADDONS_NOT_IN_PRICING = "error.addons.not.in.pricing";
    public static final String SERVICE_ADDONS_INVALID = "error.service.addons.invalid";
    public static final String SERVICE_STOCK_INVALID = "error.service.stock.invalid";
    public static final String AVAILABLE_STOCK_REQUIRED = "error.available.stock.required";
    public static final String MANAGER_LEVEL_REQUIRED = "error.manager.level.required";
    public static final String COMBO_PRICING_INVALID = "error.combo.pricing.invalid";
    public static final String SWAP_COMBO_PLAN_INVALID = "error.swap.combo.plan.order.combo";
    public static final String ADDON_TAX_INVALID = "error.addons.tax.invalid";
    public static final String MIGRATE_ENTERPRISE_INVALID = "error.migrate.enterprise.invalid";
    public static final String MIGRATE_PRICING_INVALID = "error.migrate.pricing.invalid";
    public static final String MIGRATE_PRICING_PLAN_INVALID = "error.migrate.pricing.plan.invalid";
    public static final String MIGRATE_PRICING_MISSING_PLAN = "error.migrate.pricing.missing.plan";
    public static final String MIGRATE_REPEAT_TYPE_INVALID = "error.migrate.repeat.type.invalid";
    public static final String COMBO_PLAN_INVALID = "error.combo.plan.invalid";

    public static final String ADDON_NOT_BE_AWAITING_APPROVAL = "error.addon.not.be.awaiting.approval";
    public static final String ADDON_MULTI_PLAN_NOT_FOUND = "error.addon.multi.plan.not.found";
    public static final String ADDON_NEED_APPROVED = "error.addon.need.approved";
    public static final String CURRENT_USER_CAN_NOT_APPROVE = "error.user.can.not.approve";
    public static final String OUT_OF_QUANTITY = "error.out.of.quantity";
    public static final String ADDONS_STILL_USED = "error.addon.still.used";
    public static final String CURRENT_USER_NOT_OWN = "error.addon.user.not.own";
    public static final String SUCCESS_DELETED_ALL_ADDON = "success.deleted.all.addon";
    public static final String SUCCESS_DELETED_ADDONS_NON_SUBSCRIPTION = "success.deleted.addons.non.subscription";
    public static final String CODE_ALL_SUCCESS = "delete.success.all";
    public static final String CODE_SUCCESS_WITHOUT_SUBSCRIPTION = "delete.success.with.out.subscription";
    public static final String SERVICE_STILL_USED = "error.service.still.used";
    public static final String SUCCESS_DELETED_ALL_SERVICE = "success.deleted.all.service";
    public static final String SUCCESS_DELETED_SERVICE_NON_USED = "success.deleted.service.non.used";
    public static final String TRANSACTION_API_ERR = "error.transaction.api";
    public static final String MASOFFER_API_ERR = "error.masoffer.api";
    public static final String APINFO_API_ERR = "error.apinfo.api";
    public static final String ACCESSTRADE_API_ERR = "error.accesstrade.api";
    public static final String CURRENT_USER_NOT_BE_OWNER = "error.current.user.not.be.owner";
    public static final String ADDON_MUST_NOT_UNAPPROVED = "error.addon.must.not.unApproved";
    public static final String COMBO_MUST_NOT_UNAPPROVED = "error.combo.must.not.unApproved";
    public static final String COMBO_MUST_UNAPPROVED = "error.combo.must.unApproved";
    public static final String USER_ID_MUST_NOT_BE_EMPTY = "user.id.must.not.be.empty";
    public static final String COMBO_MUST_AWAITING_APPROVE = "error.combo.must.awaitingApprove";
    public static final String SERVICE_GROUP_MUST_AWAITING_APPROVE = "error.service.group.must.awaitingApprove";
    public static final String COMBO_REJECT_COMMENT_MUST_NOT_EMPTY= "error.combo.reject.comment.must.not.empty";

    public static final String SERVICE_GROUP_REJECT_COMMENT_MUST_NOT_EMPTY= "error.service.group.reject.comment.must.not.empty";

    public static final String SERVICE_GROUP_MUST_UNAPPROVED = "error.service.group.must.unApprove";
    public static final String SERVICE_EXIST_DISABLE_MULTI_SUB = "error.service.exist.disable.multi.sub";
    public static final String COMBO_STATUS_WAS_NOT_BE_FOUND= "error.combo.status.was.not.be.found";
    public static final String COMBO_WAS_NOT_FOUND= "error.combo.was.not.found";
    public static final String CURRENT_USER_CANNOT_CHANGE_STATUS = "error.current.user.can.not.change.status";
    public static final String COMBO_CANNOT_CHANGE_STATUS = "error.combo.can.not.change.status";

    public static final String CAN_NOT_USE_MULTI_COUPON = "error.can.not.use.milti.coupon";
    public static final String ADDON_INACTIVE = "error.addon.inactive";
    public static final String COUPON_INACTIVE = "error.coupon.inactive";
    public static final String TOTAL_SUBSCRIPTION_INCORRECT = "error.total.subscription.incorrect";
    public static final String BILLING_INCORRECT = "error.billing.incorrect";
    public static final String BILLING_CANNOT_UPDATE = "error.billing.cannot.update";
    public static final String SUBSCRIPTION_WRONG_USER = "error.wrong.user";
    public static final String CAN_NOT_INIT = "error.can.not.init";
    public static final String USER_NOT_OWN_SUBSCRIPTION = "error.user.not.own.subscription";
    public static final String PARTNER_NOT_OWN_ORDER = "error.partner.not.own.order";
    public static final String SYSTEM_CAN_NOT_ALLOW_OFFLINE_PAYMENT = "error.system.can.not.allow.offline.payments";
    public static final String SYSTEM_CAN_NOT_SUPPORT_PAYMENT_METHOD_FOR_SUB = "error.system.can.not.support.payment.method";
    public static final String PAYMENT_CYCLE_PRICING_GREATER_THAN_OR_EQUAL_ADDON = "error.payment.cycle.pricing.greater.than.or.equal.addon";
    public static final String ERROR_FLAT_RATE_QUANTITY = "error.subscription.flat.rate.only.one.quantity";
    public static final String ERROR_USER_SUBSCTIPTED = "error.subscription.user";
    public static final String REDIS_ERROR = "error.redis.server";
    public static final String JSON_FORMAT_INCORRECT = "error.json.format.incorrect";
    public static final String ADDON_NEED_REQUIRED = "error.addon.need.required";
    public static final String SUBSCRIPTION_CAN_NOT_CANCEL = "error.subscription.can.not.cancel";
    public static final String SUBSCRIPTION_STATUS_IS_NOT_ACTIVE_OR_NOT_FEATURE = "error.subscription.is.not.activate.or.not.feature";
    public static final String SUBSCRIPTION_IS_NOT_OWNED = "error.subscription.is.not.owned";
    public static final String ERROR_USER_USED = "error.object.user.used";
    public static final String BILL_OUT_OF_DATE = "error.subscription.billings.outOfDate";
    public static final String NOT_ACTIVATE_AGAIN = "error.subscription.can.not.activate.again";
    public static final String NOT_ACTIVATE_PERMISSION = "error.subscription.can.not.activate.permission";
    public static final String SUBSCRIPTION_UNLIMITED = "error.subscription.is.unlimited";
    public static final String INVALID_COMBO_ADDON_DATA = "error.combo.addon.cycle.is.greater";
    public static final String COMBO_PLAN_NOT_APPROVED = "error.combo.plan.is.not.approved";
    public static final String COMBO_NOT_APPROVED = "error.combo.is.not.approved";
    public static final String COUPON_INVALID = "error.coupon.invalid";
    public static final String ADDON_INVALID = "error.addon.invalid";
    public static final String SERVICE_NOT_LATEST_VERSION = "error.service.version.invalid";
    public static final String PRICING_NOT_MAX_VERSION = "error.pricing.ver.invalid";
    public static final String COMBO_PLAN_NOT_MAX_VERSION = "error.combo.plan.ver.invalid";
    public static final String COMBO_NOT_MAX_VERSION = "error.combo.ver.invalid";
    public static final String ADDON_NOT_MAX_VERSION = "error.addon.ver.invalid";
    public static final String COMBO_PLAN_INACTIVE = "error.combo.plan.inactive";
    public static final String OBJECT_IN_USED = "error.object.in.used";
    public static final String ADDON_IN_USED = "error.addon.in.used";
    public static final String SERVICE_INVISIBLE = "error.services.invisible";
    public static final String OBJECT_IN_TRIAL = "error.object.in.trial";
    public static final String COMBO_INVISIBLE = "error.combo.invisible";
    public static final String ERROR_SUBSCRIPTION_TRIAL_ORDER_SERVICE = "error.subscription.trial.order.service.for.sme";
    public static final String SUBSCRIPTION_CAN_NOT_UPDATE = "error.subscription.can.not.update";
    public static final String PRODUCT_ORDER_CAN_NOT_UPDATE = "error.product.order.can.not.update";

    public static final String MAIL_CONTACT_INFO_FOR_ADMIN = "mail.contact.send.admin.province.hp01";
    public static final String MAIL_CONTACT_INFO_FOR_CUSTOMER = "mail.contact.send.customer.hp02";
    public static final String COMBO_NOT_FOUND = "error.combo.not.found";
    public static final String INVALID_IP_ADDRESS = "error.invalid.ip.address";
    public static final String COMBO_PLAN_IS_USED = "error.combo.used";
    public static final String COUPON_NOT_ELIGIBLE_TO_USE = "error.coupon.not.eligible.to.use";
    public static final String CAN_NOT_RENEW_SUBSCRIPTION = "error.can.not.renew.subscription";
    public static final String INVALID_PAYMENT_METHOD = "error.invalid.payment.method";

    public static final String CAN_NOT_REACTIVE_SUBSCRIPTION_SERVICE_OFF = "error.can.not.reactive.subscription.service.off";
    public static final String CAN_NOT_REACTIVE_SUBSCRIPTION_PRICING_OFF = "error.can.not.reactive.subscription.pricing.off";
    public static final String CAN_NOT_REACTIVE_SUBSCRIPTION_PRM_OFF = "error.can.not.reactive.subscription.prm.off";

    public static final String CAN_NOT_CANCEL_ONE_TIME_SUBSCRIPTION = "error.can.not.cancel.onetime.subscription";

    public static final String CAN_NOT_CANCEL_OS_SUBSCRIPTION = "error.can.not.cancel.os.subscription";
    public static final String CAN_NOT_CANCEL_ON_LIMITED_CYCLES_SUBSCRIPTION = "error.can.not.cancel.on.limited.cycles.subscription";

    public static final String CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_PRM = "error.can.not.renew.subscription.prm.off";
    public static final String CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_PRICING = "error.can.not.renew.subscription.pricing.off";
    public static final String CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_SERVICE = "error.can.not.renew.subscription.service.off";
    public static final String CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_CUSTOMER_TYPE = "error.can.not.renew.subscription.ctmt.not.available";
    public static final String CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_STATUS = "error.can.not.renew.subscription.status";
    public static final String CAN_NOT_SWAP_PRICING = "error.can.not.swap.in.final.cycle";
    public static final String ERROR_USER_SUBSCRIPTION_ADDON = "error.subscription.addon.user";
    public static final String ERROR_SERVICE_SUBSCRIPTION_ADDON = "error.subscription.addon.service";
    public static final String ERROR_MUST_GREATER_OR_EQUAL_TO_CURRENT_CYCLE = "error.subscription.number.of.cycles";
    public static final String ORDER_SERVICE_STATUS_MUST_BE_RECEIVED = "error.order.service.status.must.be.received";
    public static final String UNKNOWN_ERROR = "error.unknown";
    public static final String MISS_DATA_TRANSACTION = "error.miss.data.transaction";
    public static final String CAN_NOT_CALL_DHSXKD = "error.can.not.call.dhsxkd";
    public static final String PRICING_DOES_NOT_SUPPORT_TRIAL = "error.pricing.does.not.support.trial";
    public static final String COMBO_PLAN_DOES_NOT_SUPPORT_TRIAL = "error.combo.does.not.support.trial";
    public static final String OBJECT_ALREADY_SYNCED = "error.object.already.synced";
    public static final String TOTAL_AMOUNT_EINVOICE = "error.einvoice.amount.zero";
    public static final String HAVE_ERROR_FROM_DHSX = "error.have.error.from.product.control.center";
    public static final String CREDIT_NOTE_INVALID = "error.credit.note.has.been.applied";
    public static final String REMOTE_SYSTEM_NOT_RESPONSE = "error.remoteSystem.not.respond";
    public static final String REMOTE_SYSTEM_UNAUTHORIZED = "error.remoteSystem.response.unauthorized";
    public static final String REMOTE_SYSTEM_RESPOND_FAILURE = "error.remoteSystem.respond.failure";

    public static final String INVOICE_AUTHEN_INVALID = "error.invoice.authen.invalid";
    public static final String INVOICE_XML_INVALID = "error.invoice.xml.invalid";
    public static final String INVOICE_NOT_FOUND_COMPANY = "error.invoice.not.found.company";
    public static final String INVOICE_PATTERN_SERIAL_INVALID = "error.invoice.pattern.serial.invalid";
    public static final String INVOICE_NOT_PUBLISH = "error.invoice.not.publish";
    public static final String INVOICE_CALL_API_IMPORT_FAILED = "error.invoice.call.api.import.failed";
    public static final String INVOICE_ALREADY_EXPORTED = "error.invoice.already.exported";
    public static final String CORRESPONDING_INVOICE_NOT_FOUND = "error.corresponding.invoice.not.found";
    public static final String EXCEEDS_100_UNITS = "exceeds.100.units";
    public static final String FKEY_DUPLICATE = "error.fkey.duplicate";

    public static final String PRICING_MULTI_PLANS_IS_NOT_EMPTY = "error.pricing.multi.plan.is.not.empty";
    public static final String PACKAGE_IS_NOT_EMPTY = "error.package.item.is.not.empty";

    public static final String PRICING_MULTI_PLANS_DUPLICATE_PAYMENT_CYCLE = "error.pricing.multi.plan.duplicate.payment.cycle";

    public static final String PRICING_MULTI_PLANS_DUPLICATE_CYCLE_CODE = "error.pricing.multi.plan.duplicate.cycle.code";
    public static final String PRICING_MULTI_PLANS_DUPLICATE_PACKAGE_CODE_ALL_DB = "error.pricing.multi.plan.duplicate.package.code.all.DB";
    public static final String PRICING_MULTI_PLANS_DUPLICATE_CYCLE_CODE_ALL_DB = "error.pricing.multi.plan.duplicate.cycle.code.all.DB";
    public static final String PRICING_CUSTOMER_TYPE_CODE_INVALID = "error.pricing.customer.type.code.invalid";
    public static final String INVALID_CUSTOMER_TYPE = "error.invalid.customer.type";
    public static final String INVALID_FIELD_CAN_NOT_EDIT = "error.invalid.field.can.not.edit";
    public static final String PRICING_PLAN_HAVE_CUSTOMER_TYPE_INVALID = "error.pricing.plan.have.customer.type.invalid";
    public static final String PRICING_NOT_BELONG_TO_SERVICE = "error.pricing.not.belong.to.service";

    public static final String INVALID_TAG_PARAM = "error.invalid.tag.param.not.exist";

    public static final String CANNOT_CHANGE_PRICING_OF_ANOTHER_SERVICE = "error.cannot.change.pricing.of.another.service";
    public static final String ERROR_USER_SUBSCTIPTED_COUPON = "error.subscription.coupon.user";
    public static final String ERROR_USER_USER_INFORMATION = "error.information.user";
    public static final String PLAN_URL_ALREADY_EXISTS = "error.seo.planUrl.invalid";
    public static final String SUBSCRIPTION_INVALID = "error.subscription.invalid";
    public static final String CAN_NOT_UPDATE_ADDON = "error.service.owner.other";
    public static final String USER_HAS_BEEN_REGISTERED = "error.user.has.been.registered";
    public static final String INVALID_CAN_NOT_CHANGE_VALUE = "error.invalid.can.not.change";

    public static final String CAN_NOT_DELETED = "error.can.not.delete";

    public static final String OBJECT_DEFAULT = "error.object.is.default";

    public static final String KEY_CUSTOMER_AUTO_MIGRATE = "common-customer-auto-migration";

    public static final String MAIL_SERVER_LIMITED = "error.mail.limited";

    public static final String ACCESS_DENIED = "error.access.denied";

    public static final String EXISTS_PRODUCT_SKU = "error.exists.services.sku";


    // MessageConst kích hoạt tài khoản thất bại
    public static final String USER_ACTIVATED = "error.user.activated";
    public static final String USER_EXPIRED_ACTIVATION_KEY = "error.user.activation.key.expired";
    public static final String USER_NOT_MATCHED_ACTIVATION_KEY = "error.user.activation.key.not.matched";

    public static final String AUTHENTICATED_TRAFFIC_WALLET = "error.traffic.authenticated.wallet";
    public static final String SUCCESS = "SUCCESS";
    public static final String API_EXCEPTION = "error.api.exception";
    public static final String NOT_LATEST_VERSION = "error.item.not.latest.version";
    public static final String PACKAGE_NOT_REGISTERED = "error.package.not.registered";
    public static final String PACKAGE_NOT_IN_SOLUTION = "error.package.not.belong.to.solution";
    public static final String CAN_NOT_UPDATE = "error.can.not.update";
    public static final String STATE_ITEM_EXISTS = "error.state.can.not.exists";
    public static final String NOT_SUPPORTED = "error.not.supported";

    public static class Transaction {
        public static final String RANGE = "error.valid.range";
        public static final String CAN_NOT_EDIT_USER_SAAS = "error.can.not.edit.user.saas";

    }

    public static class Validation {
        public static final String DATA_FROMAT = "error.data.format";
        public static final String RANGE = "error.valid.range";
        public static final String SIZE = "error.valid.size";
        public static final String NOT_NULL = "error.valid.not.null";
        public static final String LENGTH = "error.valid.length";
        public static final String ASSERT_FALSE = "error.valid.assert.false";
        public static final String ASSERT_TRUE = "error.valid.assert.true";
        public static final String DECIMAL_MAX = "error.valid.decimal.max";
        public static final String DECIMAL_MIN = "error.valid.decimal.min";
        public static final String DIGITS = "error.valid.digits";
        public static final String EMAIL = "error.valid.email";
        public static final String FUTURE = "error.valid.future";
        public static final String FUTURE_OR_PRESENT = "error.valid.future.or.present";
        public static final String MAX = "error.valid.max";
        public static final String MIN = "error.valid.min";
        public static final String NEGATIVE = "error.valid.negative";
        public static final String NEGATIVE_OR_ZERO = "error.valid.negative.or.zero";
        public static final String NOT_BLANK = "error.valid.not.blank";
        public static final String NOT_EMPTY = "error.valid.not.empty";
        public static final String NULL = "error.valid.null";
        public static final String PAST = "error.valid.past";
        public static final String PAST_OR_PRESENT = "error.valid.past.or.present";
        public static final String PATTERN = "error.valid.pattern";
        public static final String POSITIVE = "error.valid.positive";
        public static final String POSITIVE_OR_ZERO = "error.valid.positive.or.zero";
        public static final String PHONE = "error.valid.phone";
        public static final String URL = "error.valid.url";
        public static final String DATA_FORMAT = "error.data.format";
        public static final String DATA_TYPE_MISMATCH = "error.data.type.mismatch";
        public static final String PATTERN_PHONE = "error.valid.phone.pattern";
        public static final String PATTERN_CURRENCY = "error.valid.currency.pattern";
        public static final String PATTERN_PAYMENT_METHOD = "error.valid.payment.method.pattern";
        public static final String FIELD_WRONG_JSON_FORMAT = "error.field.wrong.json.format";
        public static final String VALUE = "error.valid.value";
        public static final String PRIORITY = "error.valid.value";

        private Validation() {
        }
    }

    public static class EnterpriseError{
        public static final String NAME_NOT_EMPTY = "error.enterprise.name.not.empty";
        public static final String NOT_FOUND = "error.object.not.found";
        public static final String IDS_NOT_BE_EMPTY = "error.enterprise.ids.not.be.empty";
        public static final String ACTIVE_STATUS_INVALID = "error.enterprise.active.status.invalid";
        public static final String ARCHIVE_STATUS_INVALID = "error.enterprise.archive.status.invalid";
        public static final String EMAIL_NULL = "error.enterprise.email.not.null";
        public static final String EMAIL_DUPLICATED = "error.enterprise.email.duplicated";
        public static final String TIN_NULL = "error.enterprise.tin.not.null";
        public static final String TIN_DUPLICATED = "error.enterprise.tin.duplicated";
        public static final String NAME_NULL = "error.enterprise.name.not.null";
        public static final String NAME_DUPLICATED = "error.enterprise.name.duplicated";
        public static final String PHONE_NULL = "error.enterprise.phone.not.null";
        public static final String PHONE_DUPLICATED = "error.enterprise.phone.duplicated";
        public static final String PERSONAL_CERT_DUPLICATED = "error.enterprise.personalCert.duplicated";
        public static final String GENERAL_DESC_NULL = "error.enterprise.general.desc.not.null";
        public static final String BUSINESS_REGISTRATION_URLS_NULL = "error.enterprise.business.registration.urls.not.null";
        public static final String ENTERPRISE_ID_NULL = "error.enterprise.id.not.null";
        public static final String DUPLICATE_EMAIL = "error.enterprise.duplicate.email";
        public static final String SUBSCRIPTION_EXISTED = "error.enterprise.subscription.existed";
        public static final String BIRTHDAY_GREATER_THAN_TODAY = "error.birthday.greater.than.today";
    }

    public static class CustomerContactError {
        public static final String DUPLICATE_EMAIL = "error.customer.contact.duplicate.email";
        public static final String DUPLICATE_PHONE = "error.customer.contact.duplicate.personalPhone";
        public static final String DUPLICATE_IDENTITYNO = "error.customer.contact.duplicate.identityno";
        public static final String DUPLICATE_PERSONAL_EMAIL = "error.customer.contact.duplicate.personalEmail";
        public static final String DUPLICATE_TIN = "error.customer.contact.duplicate.tin";
        public static final String NOT_FOUND = "error.customer.contact.not.found";
        public static final String CONTACT_NAME_NULL = "error.customer.contact.name.null";
        public static final String PHONE_NULL = "error.customer.contact.phone.null";
        public static final String CONTACT_PHONE_EMPTY = "error.customer.contact.contactPhones.empty";
        public static final String EMAIL_NULL = "error.customer.contact.email.null";
        public static final String CONTACT_EMAIL_EMPTY = "error.customer.contact.contactEmails.empty";
        public static final String TIN_NULL = "error.customer.contact.tin.null";
        public static final String CONTACT_TIN_DUPLICATED = "error.customer.contact.tin.duplicated";
        public static final String CONTACT_EMAIL_DUPLICATED = "error.customer.contact.email.duplicated";

        public static final String FILE_SIZE_EXCEED = "error.customer.contact.file.size.exceed";
        public static final String FILE_CANNOT_BE_OPENED = "error.customer.contact.file.cannot.be.opened";
        public static final String FILE_REDUNDANT_COLUMN = "error.customer.contact.file.redundant.column";
        public static final String FILE_DUPLICATE_COLUMN = "error.customer.contact.file.duplicate.column";
        public static final String FILE_MISSING_COLUMN = "error.customer.contact.file.missing.column";
        public static final String FILE_IS_EMPTY_DATA = "error.customer.contact.file.is.empty.data";
        public static final String CUSTOMER_TYPE_INVALID = "error.customer.contact.customer.type.is.invalid";
    }

    public static class CustomerGroupError{
        public static final String NOT_FOUND = "error.customer.group.not.found";
        public static final String DUPLICATE_NAME = "error.customer.group.duplicate.name";
        public static final String DUPLICATE_EMAIL = "error.customer.group.duplicate.email";
        public static final String ENTERPRISE_ID_NULL = "error.customer.group.enterpriseId.null";
        public static final String UPLOADED_CUSTOMER_ID_NULL = "error.customer.group.uploadedCustomerId.null";
        public static final String CONTACT_ID_NULL = "error.customer.group.contactId.null";
        public static final String CUSTOMER_UPLOADED_IDS_NOT_BE_EMPTY = "error.customer.uploaded.ids.not.be.empty";
        public static final String GROUP_SIZE_EXCEED = "error.customer.group.size.exceed";
        public static final String CUSTOMER_TYPE_INVALID = "error.customer.group.customer.type.is.invalid";
    }

    public static class MarketingCampaignError {
        public static final String CAMPAIGN_NOT_FOUND = "error.campaign.not.found";
        public static final String INVALID_JSON_FORMAT = "error.campaign.invalid.json.format";
        public static final String DUPLICATE_NAME = "error.campaign.duplicate.name";
        public static final String INVALID_BUDGET = "error.campaign.invalid.budget";
        public static final String COPY_ARCHIVED_CAMPAIGN = "error.campaign.copy.from.archived.campaign";
        public static final String UPDATE_INVALID_STATUS_CAMPAIGN = "error.campaign.update.campaign.in.invalid.status";
    }

    public static class MCActivityError {
        public static final String MC_ACTIVITY_NOT_FOUND = "error.mc.activity.not.found";
        public static final String MC_ACTIVITY_EXISTED = "error.mc.activity.existed";
    }

    public static class McActionEmailAutoSmsError {
        public static final String MC_ACTION_EMAIL_AUTO_SMS_NOT_FOUND = "error.mc.action.email.auto.sms.not.found";
        public static final String MC_ACTION_EMAIL_AUTO_SMS_SENT = "error.mc.action.email.auto.sms.sent";
    }

    public static class PageBuilderError {
        public static final String ACTIVE = "error.pagebuilder.active";
        public static final String INVALID_VERSION = "error.pagebuilder.invalid.version";
    }

    public static class ShoppingCartError {
        public static final String INVALID_USER_ID = "error.shoppingCart.invalid.userId";
        public static final String EMPTY_CONTENT = "error.shoppingCart.content.empty";

        public static final String INVALID_STATUS_ADDON = "error.shoppingCart.invalid.addon.status";

        public static final String INVALID_STATUS_COMBO_PLAN = "error.shoppingCart.invalid.combo_plan.status";

        public static final String INVALID_STATUS_PRICING = "error.shoppingCart.invalid.pricing.status";
    }

    public static class ProductSolutions {
        public static final String SOLUTION_CODE_EXIST = "error.solution.code.exist";
        public static final String PACKAGE_NAME_EXIST = "error.package.name.exist";
        public static final String PACKAGE_DEFAULT_IS_NOT_NULL = "error.package.default.is.not.null";
        public static final String PACKAGE_EXISTS_FOR_SUBSCRIPTION = "error.package.exist.for.subscription";
        public static final String SOLUTION_EXISTS_FOR_SUBSCRIPTION = "error.solution.exist.for.subscription";
        public static final String PACKAGE_NOT_ALLOWED = "error.package.not.allowed";
        public static final String PACKAGE_NOT_FOUND = "error.package.not.found";
        public static final String SOLUTION_NOT_ALLOWED = "error.solution.not.allowed";
        public static final String SOLUTION_NOT_FOUND = "error.solution.not.found";
        public static final String SOLUTION_REJECT_COMMENT_MUST_NOT_EMPTY = "error.solution.reject.comment.must.not.empty";
        public static final String PACKAGE_REJECT_COMMENT_MUST_NOT_EMPTY = "error.package.reject.comment.must.not.empty";

        public static final String PACKAGE_CODE_EXIST = "error.package.code.exist";
    }

    public static class Logistic {
        public static final String CARRIER_CODE_EXIST = "error.carrier.code.exist";
        public static final String CARRIER_PARTNER_CODE_EXIST = "error.carrier.partner.code.exist";
        public static final String CARRIER_NAME_EXIST = "error.carrier.name.exist";
    }
    public static class Sla {
        public static final String METRIC_GROUP_NAME_EXIST = "error.metric.group.name.exist";
        public static final String METRIC_GROUP_NOT_FOUND = "error.metric.group.not.found";
        public static final String STATUS_METRIC_ACTIVE = "error.sla.metric.active";
        public static final String STATUS_POLICY_ACTIVE = "error.sla.policy.active";
        public static final String POLICY_NAME_EXIST = "error.sla.policy.name.exist";
        public static final String STATUS_SLA_TEMPLATE_ACTIVE = "error.sla.template.active";
        public static final String USED_METRIC_TEMPLATE = "error.used.metric.template";
        public static final String USED_SLA_TEMPLATE_POLICY = "error.used.sla.template.policy";
    }

    public static class ProfileDevice {
        public static final String PROFILE_NAME_EXIST = "error.profile.name.exist";
        public static final String PROFILE_MODEL_EXIST = "error.profile.model.exist";
        public static final String PROFILE_COMMAND_NAME_EXIST = "error.profile.command.name.exist";
        public static final String PROFILE_TELEMETRY_NAME_EXIST = "error.profile.telemetry.name.exist";
        public static final String PROFILE_TELEMETRY_DISPLAY_NAME_EXIST = "error.profile.telemetry.display.name.exist";
        public static final String PROFILE_ATTRIBUTE_DISPLAY_NAME_EXIST = "error.profile.attribute.display.name.exist";
        public static final String PROFILE_ATTRIBUTE_NAME_EXIST = "error.profile.attribute.name.exist";
    }

    public static class SubscriptionError {
        public static final String INVALID_REGISTER_ITEM = "error.subscription.invalid.register.item";
        public static final String BUY_ONLY_SERVICE_BUT_HAS_VARIANT= "error.subscription.buy.only.service.but.has.variant";
    }

    public static class Topic{
        public static final String TOPIC_APPROVE_STATUS_CAN_NOT_CHANGE = "error.topic.approve.status.can.not.change";
        public static final String UNAPPROVED = "error.object.unapproved";
    }

    public static class AffiliateConfigError{
        public static final String NON_NULL = "error.affiliate.config.can.not.null";
        public static final String WRONG_JSON_FORMAT = "error.affiliate.config.wrong.json.format";
        public static final String INVALID_MAX_MEMBER_LEVEL = "error.affiliate.config.invalid.max_member_level";
        public static final String INVALID_SIZE_MEMBER_CONFIG_LIST = "error.affiliate.config.invalid.size.of.member_config_list";
        public static final String INVALID_COMMISSION_VALUE = "error.affiliate.config.invalid.commission.value";
        public static final String INVALID_DOMAIN = "error.affiliate.config.invalid.domain";
        public static final String EXISTS_DOMAIN = "error.affiliate.config.exists.domain";
    }

    public static class AffiliateLinkConfigError{
        public static final String NON_NULL = "error.affiliate.link.config.can.not.null";
        public static final String LINK_CODE = "error.affiliate.link.config.create.link_code";
    }

    public static class AffiliateLink{
        public static final String INACTIVE = "error.affiliate.link.inactive";
        public static final String SHORTEN_LINK_BOM_SO = "error.get.shorten_link.from.bom_so";
        public static final String SHORTEN_LINK_VNPT = "error.get.shorten_link.from.vnpt";
        public static final String SERVICE_NOT_ACTIVE = "error.affiliate.link.service.or.combo.not.active";
        public static final String PRICING_NOT_ACTIVE = "error.affiliate.link.pricing.or.combo.plan.not.active";
        public static final String DO_NOT_HAVE_PERMISSION = "error.affiliate.user.do.not.have.permission";
        public static final String CAN_NOT_USE_SERVICE = "error.affiliate.link.can.not.use.service";
        public static final String CAN_NOT_USE_PRICING= "error.affiliate.link.can.not.use.pricing";
        public static final String CAN_NOT_USE_CATEGORY = "error.affiliate.link.can.not.use.category";
        public static final String CAN_NOT_CREATE_TYPE_OTHER = "error.affiliate.link.can.not.create.type.other";
    }

    public static class AffiliateCommission{
        public static final String SERVICE_PRODUCT_EMPTY = "error.service_product.empty";
        public static final String SERVICE_PRODUCT_DELETED = "error.service_product.is.deleted";
        public static final String MEMBER_DELETED = "error.member.is.deleted";
        public static final String CAN_NOT_USE_PRICING= "error.affiliate.commission.can.not.use.pricing";
    }

    public static class ExcelError {
        public static final String FILE_NAME_TOO_SHORT = "error.xlsx.name.too.short";
        public static final String FILE_NAME_TOO_LONG = "error.xlsx.name.too.long";
        public static final String INVALID_EXTENSION = "error.xlsx.invalid.extension";
        public static final String FILE_SIZE_EXCEED = "error.xlsx.file.size.exceed";
        public static final String REDUNDANT_COLUMN = "error.xlsx.redundant.column";
        public static final String DUPLICATE_COLUMN = "error.xlsx.duplicated.column";
        public static final String MISSING_COLUMN = "error.xlsx.missing.column";
    }

    public static class EnterpriseHouseHoldRegister {

        //Email bị trùng với DEV/ADMIN thì không được đăng ký
        public static final String EXISTS_EMAIL = "error.exists.email";

        //Sđt bị trùng với DEV/ADMIN thì không được đăng ký
        public static final String EXISTS_PHONE = "error.exists.phone";

        // Phone bị trùng với DEV/ADMIN thì không cho phép sửa/tạo mới
        public static final String EXISTS_PHONE_DEV_ADMIN = "error.exists.phone.dev.admin";

        // Phone bị trùng với nhân viên khác cùng doanh nghiệp thì không cho phép sửa/tạo mới
        public static final String EXISTS_PHONE_COLLEAGUE = "error.exists.phone.colleague";

        // Email bị trùng với DEV/ADMIN thì không cho phép sửa/tạo mới
        public static final String EXISTS_EMAIL_DEV_ADMIN = "error.exists.email.dev.admin";

        // Email bị trùng với nhân viên khác cùng doanh nghiệp thì không cho phép sửa/tạo mới
        public static final String EXISTS_EMAIL_COLLEAGUE = "error.exists.email.colleague";

        //Cả Email và phone bị trùng với KHDN/HKD thì vẫn được đăng ký tiếp sau khi đã ấn xác nhận
        public static final String EXISTS_EMAIL_PHONE_ENTERPRISE_HOUSE_HOLD = "error.exists.email.phone.enterprise.house_hold";

        //Email bị trùng với KHDN/HKD thì vẫn được đăng ký tiếp sau khi đã ấn xác nhận
        public static final String EXISTS_EMAIL_ENTERPRISE_HOUSE_HOLD = "error.exists.email.enterprise.house_hold";

        //Phone bị trùng với KHDN/HKD thì vẫn được đăng ký tiếp sau khi đã ấn xác nhận
        public static final String EXISTS_PHONE_ENTERPRISE_HOUSE_HOLD = "error.exists.phone.enterprise.house_hold";

        // Email của employee bị trùng với KHDN/HKD cha khác
        public static final String EXISTS_EMAIL_DIFF_ENTERPRISE_HOUSE_HOLD = "error.exists.email.diff.enterprise.house_hold";


    }

    public static class DriveError {
        public static final String DRIVE_PERMISSION_DENIED = "error.drive.permission.denied";
        public static final String DRIVE_STORAGE_CAPACITY = "error.drive.storage.capacity.exceeded";
        public static final String FILE_FOLDER_NOT_EXIST = "error.file.folder.not.exist";
        public static final String FILE_FOLDER_MUST_CHOOSE = "error.must.choose.file.folder";
        public static final String DRIVE_CONNECTION_NOT_FOUND = "error.drive.connection.not.found";
        public static final String DRIVE_NOT_FOUND = "error.drive.not.found";
        public static final String CAN_NOT_MOVE_TO_FILE = "error.can.not.move.to.file";
        public static final String NOT_FOLDER = "error.drive.not.folder";
        public static final String DRIVE_CONFIG_DELETE_OUT_DATE = "error.drive.delete.out.date";
    }

    public static class ServiceGroupError {
        public static final String EXISTED_CODE = "error.service.group.code.existed";
        public static final String ID = "error.service.group.not.found";
        public static final String STATUS = "error.service.group.not.found";

    }

    public static class BiometricAuthentication {
        public static final String UNTRUSTED_CHALLENGE = "error.biometric.auth.untrusted.challenge";
        public static final String UNTRUSTED_E_SIGNATURE = "error.biometric.auth.untrusted.e_signature";
        public static final String INVALID_CHALLENGE_FORM = "error.biometric.auth.invalid.challenge.form";
        public static final String INVALID_CHALLENGE_DATA = "error.biometric.auth.invalid.challenge.data";
        public static final String EXPIRED_CHALLENGE = "error.biometric.auth.expired.challenge";
        public static final String USER_DEVICE_BIOMETRIC_AUTH_EXISTED = "error.biometric.auth.user.device.existed";
        public static final String USER_DEVICE_BIOMETRIC_AUTH_NOT_EXISTED = "error.biometric.auth.user.device.not.existed";
    }

    public static final String MESSAGE_INVALID_CAPTCHA_TOKEN = "Invalid ReCaptcha Token.";
    public static final String TAX_SME_INVALID = "error.invalid.tax.sme";

    public static class ErrorCode {

        public static final String INVALID_CAPTCHA_TOKEN = "error.invalid.captcha.token";
    }

    public static class Variant {
        public static final String OUT_OF_STOCK = "error.variant.out.of.stock";
        public static final String NOT_EXIST_PRICING = "error.variant.not.exist.pricing";
    }

    public static class Partner {

        public static final String INVALID_TOKEN = "error.partner.invalid.token";
        public static final String NOT_FOUND = "error.partner.not.found";
        public static final String EXPIRED_TOKEN = "error.partner.expired.token";
    }

    public static class ApiGwKHCN {
        public static final String FAILURE_RESPONSE = "error.apigwkhcn.failure.response";
        public static final String NOT_VINAPHONE_MSISDN = "error.apigwkhcn.not.vinaphone.msisdn";
        public static final String NOT_PREPAID_VINAPHONE_MSISDN = "error.apigwkhcn.not.prepaid.vinaphone.msisdn";
        public static final String POSTPAID_VINAPHONE_MSISDN = "error.apigwkhcn.postpaid.vinaphone.msisdn";
    }

    public static class WorkOrder {
        public static final String ASSIGN_CHILD_AS_PARENT = "error.work_order.assign.child.as.parent";
    }

    public static class WorkOrderChecklist {
        public static final String NOT_FOUND_PHASE_AND_STEP_IN_CHECKLIST = "error.checklist.not.found.phase.and.step";
    }

    public static class SlaMetric {
        public static final String LABEL_ALREADY_EXISTS = "sla.metric.label.already.exists";
    }
}
