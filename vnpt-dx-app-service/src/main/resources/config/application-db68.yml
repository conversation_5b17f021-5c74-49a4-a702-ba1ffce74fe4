spring:
  data:
    mongodb:
      user: vnpt-mongo
      password: vnptmongo@2021
      uri: mongodb://************:27037/masterdata
  flyway:
    enabled: false
    url: *******************************************
    user: vnpt
    password: vnpt@654321
    schemas: vnpt_dev
  application:
    name: demo
  master:
    datasource:
      driver: org.postgresql.Driver
      url: *******************************************
      username: vnpt
      password: vnpt@654321
      maximum-pool-size: 20
      minimum-idle: 6
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      leak-detection-threshold: 200000
    hikari:
      data-source-properties:
        stringtype: unspecified
  slave:
    datasource:
      driver: org.postgresql.Driver
      url: *******************************************
      username: vnpt
      password: vnpt@654321
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      leak-detection-threshold: 200000
    hikari:
      data-source-properties:
        stringtype: unspecified
  jpa:
    show-sql: true
    hibernate:
      # Hibernate_sequence' doesn't exist
      use-new-id-generator-mappings: false
      # Drop n create table, good for testing, comment this in production
      ddl-auto: none
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        default_schema: vnpt_dev
        format_sql: false
  redis:
    host: ************
    password: vnptredis@2021
    port: 6399

server:
  port: 8083
  prefix: DEV
web:
  host: https://stagingonedx.vnpt-technology.vn:6443
web-workplace:
  host: https://workplace-stagingonedx.vnpt-technology.vn:6443
batch:
  active: false
  limit:
    day:
      ev13: 1
  core_thread: 10
  max_thread: 10
  queue_capacity: 100

email:
  from: <EMAIL>
  password: Onedx@123!@
  core_thread_send_mail: 1
  max_thread_send_mail: 1
  queue_capacity_send_mail: 500
  core_thread_read_mail: 1
  max_thread_read_mail: 1
  queue_capacity_read_mail: 2

email-helper:
  core_thread_send_mail: 1
  max_thread_send_mail: 1
  queue_capacity_send_mail: 100
  core_thread_read_mail: 1
  max_thread_read_mail: 1

message:
  core_thread_send_message: 10
  max_thread_send_message: 20
  queue_capacity_send_message: 500
  core_thread_read_message: 1
  max_thread_read_message: 1
  queue_capacity_read_message: 2

kafka:
  prod-active: false
  bootstrap: ************:29092
  group-id: backend_app-35
  topic-consumer: ONESME-CONSUMER-SV35
  topic-producer: ONESME-TOPIC-SV35
  timeout: 30000
  username: admin
  password: admin-secret

transaction:
  secret:
    key: 36e3c5b5a6d53fce2a2cc23d46ee5987
  merchant_service_id: 3502
  token: ********************************
  url: https://sandboxpay.vnptmedia.vn/
  init_url: https://sandboxpay.vnptmedia.vn/rest/payment/v1.0.5/init
  base_url: https://sandboxpaydev.vnptmedia.vn/rest/payment/v1.0.5/
  qr_init_url: https://qrcodegw.vnptmoney.vn/rest/qrcodegw-v2/V1.0.0/api

dhsxkd:
  url-api-customer-info: https://api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracuu_thongtin_kh
  url-api-personal-info: https://api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracuu_thongtin_khcn
  url-api-update-sub: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_update_hopdong
  url-api-extend-sub: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_extend_sub
  url-api-change-sub: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_change_sub
  url-api-teminate-sub: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_teminate_sub
  url-api-official-register-sub: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_official_register_sub
  url-api-tiepnhan-yc-shop: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_tuvan_tiepnhan_yeucau
  url-api-get-list-dichvu-my-vnpt: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/getdichvumy_vnpt
  url-api-get-list-loai-hinh: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/getloaihinhs
  url-api-sme-cancel-order: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_cancel_order
  url-api-sme-tracking-order: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_tracking_order
  url-api-get-sme-list-order: https://api-dev-onebss.vnpt.vn/esb/mediagw/api/sme_list_order
  timeout: 30000
  note-call-api-dhsxkd: STAGING
  header:
    token:
      key: erp-token
      value: API-CSKH-ECB5835C0D98B09884AC5B811799D02F60D235655AF57A1976523B04155B3DE6
    acc:
      key: erp-acc
      value: dhsxkd
    pwd:
      key: erp-pwd
      value: apidhsx@1857610
    tokenId:
      key: token-id
      value: DEV_ERP
    tokenKey:
      key: token-key
      value: 255bd67b877a9e0fe96c47233bc7f2ca

ONE_BSS:
  domain: https://api-dev-onebss.vnpt.vn
  url-api-get-list-traffic-wallet: https://api-onebss.vnpt.vn/esb/mobile_solutions/thongtin_vi_luuluong
  url-api-share-traffic: https://api-onebss.vnpt.vn/esb/mobile_solutions/chiase_luuluong
  url-api-accuracy-traffic: https://api-onebss.vnpt.vn/esb/mobile_solutions/xacthuc
  url-api-send-otp: https://api-onebss.vnpt.vn/esb/mobile_solutions/send_otp
  url-check-participant: http://mnp_api.vnpt.vn/mapi/dataservices/check_participant
  header:
    token-id: ONESME
    token-key: Tge3EotGgoVijMyFCFfxdIn7nxQilrTZ
  secret-key : xc1sQIiL63LD6J0cX8r0ebObaCGB0cVj

mail:
  smtp:
    enable: true
    auth: true
    starttls:
      enable: false
    host: mail.vnpt-technology.vn
    port: 25

invoice:
  account:
    username: hpservice
    password: "123456aA@"
  urlDetail: https://hpservicetest.vnpt-invoice.com.vn/PortalService.asmx
  urlImport: https://hpservicetest.vnpt-invoice.com.vn/publishservice.asmx
  urlConfirm: https://hpservicetest.vnpt-invoice.com.vn/businessservice.asmx

masoffer:
  url: https://postback-vnpt.dev.masoffer.tech/transactions
  url_at: https://api.accesstrade.vn/v1/postbacks/conversions
  token_at: 1gHa9fels7QNnmeM-eBdNJZH52RLQReD
  secret: ZGV2OmJhbWJvb2Rldg==
  tracking_verify_string: masoffer_state:vnpt_masoffer
  basic_token: bWFzb2ZmZXJfc3RhdGU6dm5wdF9tYXNvZmZlcg==
  core_thread_send_postback: 1
  max_thread_send_postback: 1
  queue_capacity_send_postback: 100

apinfo:
  url: https://newapi.adpia.vn/merchant/postback/onesme

sys-econtract:
  url:
    api: https://econtract-api-demo.vnptit3.vn
    api_create_draft: https://econtract-api-dev.vnptit3.vn
  client_id: <EMAIL>
  client_secret: MSRJQazq6tFk0BIiqMC4knKg92O2INSk
  token_id: 98a0d9c9-34c7-4044-bffb-c913d733b156
  token_key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCY/AeDrgdONvTOucWbnGGX5ZqgCQI/GHxXaGyNk93/QxdzTU1xJqcM0qpE5oJ+brVyYTG+vnjqr4SqfxNky+ze2ck0q5NHn4cOJHNkeqqW7TQf8ctGuWBbUWHsjJdXfV7/Z20RE2A4QUqP6EKL/fdf8ZPOT4g8ey8JdksJe/f8WQIDAQAB
  sme_client_id: <EMAIL>
  sme_client_secret: c5f42f08da8689458f652e066a0d758a

captcha:
  site-key: 6LdAieMcAAAAAO9kAiBr1nTRIOXKIPd90lLu_esh
  client-secret: 6LdAieMcAAAAAKLpMJmnHfgM6bEX6VtVv3E_z0Qh
  site-verify: https://www.google.com/recaptcha/api/siteverify
  threshold: 0.5

site-map:
  urlSet:
    xmlns: http://www.sitemaps.org/schemas/sitemap/0.9
    xmlns-news: http://www.google.com/schemas/sitemap-news/0.9
    xmlns-xhtml: http://www.w3.org/1999/xhtml
    xmlns-mobile: http://www.google.com/schemas/sitemap-mobile/1.0
    xmlns-image: http://www.google.com/schemas/sitemap-image/1.1
    xmlns-video: http://www.google.com/schemas/sitemap-video/1.1
  changeFreq: never
  staticWeb:
    - /sme-portal
    - /sme-portal/login
    - /sme-portal/register

auto-report:
  enable: false
  googleScript: https://script.google.com/macros/s/AKfycbwCPSgzIKM5kxwQ_HktXlwkVPTLq_hS7T1x1X8leRGwbHD7EXknZe2J5XVQ9PksGHhfJg/exec
  tokenKey: X-ONE-SI-TOKEN
  tokenValue: OGNkZWViMzItNTllNC03MGZiLTg1YTEtMGZhZWM1YjY3M2FlOjFkNjIwYTMxLTNmYmQtMTA0MC1mMjIyLWRjNTJlZDJkNmI5NA==
  api_report: http://*************:8080/api/sync
  project_id: 103
  data_count: 2

sms:
  enable: false
  autoReplaceVietnamese: true
  smsUrl: http://mkt.vivas.vn:9380/SMSBNAPINEW/sendsms
  branchName: VNPT.Tech
  username: VNPTTECH
  password: vivas@123

bizfly:
  enable: false
  maxMailSendPerTime: 50
  maxMailSendPerHour: 200
  maxMailSendPerDay: 5000
  url: https://apicampain.bizfly.vn/api/customer/simple-mail/send
  appKey: 02767a35-ba50-4caa-ba2e-d82e0f439ced
  token: 7d70dd4def39d66bc07a8da40a7f3ec87761bb8af80d96588c7011112972d595d7cacc9eae48616801d784c3a3c7245856b942fcddcb09d9d004e343af1a32db
  projectToken: 684b5d8d-2d61-413f-866d-b23509c37fb9
  fromToAdmin: <EMAIL>
  fromToOther: <EMAIL>
  fromName: "oneSME"

bizfly-auto:
  enable: false
  maxMailSendPerTime: 50
  maxMailSendPerHour: 200
  maxMailSendPerDay: 5000
  url: https://apicampain.bizfly.vn/api/customer/automation/import
  token: 1117d70dd4def39d66bc07a8da40a7f3ec87761bb8af80d96588c7011112972d595d7cacc9eae48616801d784c3a3c7245856b942fcddcb09d9d004e343af1a32db
  projectToken: 111684b5d8d-2d61-413f-866d-b23509c37fb9
  appKey: 11102767a35-ba50-4caa-ba2e-d82e0f439ced

tmdtReport:
  username: online.gov.vn
  password: 7d70dd4def39

collectInfoPreOrder:
  apiKey: 6eb3f65d-de71-4d7e-9484-96a99ca812b0

sms-vinaphone:
  enable: false
  maxSmsSendPerTime: 200
  url: http://************:8888/smsbn/api
  labelId: 168171
  contractTypeId: 1
  contractId: 13644
  agentId: 462
  apiUser: onesme
  apiPass: 123456aA@
  username: sme_khdn_khcn

bizfly-customer-identify:
  enable: true
  maxMailSendPerTime: 50
  maxMailSendPerHour: 200
  maxMailSendPerDay: 5000
  url: https://apicampain.bizfly.vn/api/customer/simple-mail/send
  appKey: 02767a35-ba50-4caa-ba2e-d82e0f439ced
  token: 7d70dd4def39d66bc07a8da40a7f3ec87761bb8af80d96588c7011112972d595d7cacc9eae48616801d784c3a3c7245856b942fcddcb09d9d004e343af1a32db
  projectToken: 684b5d8d-2d61-413f-866d-b23509c37fb9
  fromToAdmin: <EMAIL>
  fromToOther: <EMAIL>
  fromName: "oneSME"
  periodTime: 10000

workplace:
  clone-role-default: /workplace/api/role/create
  vnptidp:
    clientId: Alq21cWI_Pm0LqvlVNkmZG0ivMwa
    clientSecret: Pk3hbR0drmcbSoIMyVdldA6jVbWxRbE_Iy17Rlpl2XAa
    passwordGrantType: password
    scope: openid
    adminName: admin
    adminPassword: v_}xhA!t@b52
    registerUri: https://gateway-onegov.vnpt.vn/admin/api/users/register
    tokenUri: https://gateway-onegov.vnpt.vn/oauth2/token
    changePasswordUri: https://gateway-onegov.vnpt.vn/admin/api/account/change-password
    changePasswordAdminUri: https://gateway-onegov.vnpt.vn/admin/api/account/reset-password

dxservice:
  task: http://backend-task-mgmt:8087
  workplace: http://backend-workplace:8085
  auth: http://localhost:8082
  inventory: http://***********:9090
  notifications: http://localhost:9002
  devices: http://localhost:9002

token:
  inventory: eyJhbGciOiJIUzUxMiJ9.***********************************************************.HB_3Nw2vsCoDWCq-M9_xbmMQNNHjWrqWUTa2noqS8_siFUghqJGZuQAJpSKPEOCMCR-xpSx7Nva6WGuVQ3MxmQ
nginx:
  username: admin
  password: admin
  theme-uri: http://************:80/set_default_var
  old-value: ************:88
  new-value: ************:89

apigw-khcn:
  urlInquire: https://sandboxpay.vnptmedia.vn/rest/collection/v2.0.0/inquire_commom
  urlPay: https://sandboxpay.vnptmedia.vn/rest/collection/v2.0.0/outside/pay_bill
  bearerToken: ********************************
  version: 2.0.0
  partnerId: 262998
  serviceProviderId: VNP
  channelId: 1
  privateKey: ********************************
warning-system:
  monitor-period: 5000
  tele-chat-ids-max-pool-size-connection: -1002345705799
  tele-chat-ids-failed-jdbc-connection: -1002340550859
  service-name: service
  environment: local
  millisecond-warning-interval: 100000
  monitor-replication-lag:
    period: 60000
    count-threshold: 10
api-log:
  enabled: true
  delete-log-before-months: 2
  schedule-delay-mongo-log-flush: 3000
qr:
  merchantClientId: 31544
  merchantCode: 21500
  merchantName: smartca
  terminalId: 12310
  secretKey: 956200acafec010f72311b1142b9ee0b
  init_url: https://sandboxpaydev.vnptmedia.vn/rest/vietqr/merchant/1.0.0
  apiKey: b568949b7155c8772d9c871919c59c2b
mongo_batch:
  log_perform_thread_pool:
    enabled: false
    fixed_rate_collect: 5000
    fixed_delay_flush: 30000
api-ctip:
  credential:
    url: https://ctip.vnpt.vn/api/v1/account-leak/credentials/enrichment
    key: x-key
    value: a631d2e4-3b67-4694-96b2-df960826c3b2
solution:
  list:
    per-domain:
      max-size: 4