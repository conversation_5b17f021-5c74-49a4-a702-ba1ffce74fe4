-- Bảng: vnpt_dev.sla_metric_groups
-- <PERSON><PERSON> tả: <PERSON><PERSON><PERSON><PERSON> lý danh sách các nhóm chỉ số của hệ thống
drop TABLE if exists vnpt_dev.sla_metric_groups;
drop TABLE if exists vnpt_dev.sla_attributes;
drop TABLE if exists vnpt_dev.sla_metrics;
drop TABLE if exists vnpt_dev.sla_metric_time_based;
drop TABLE if exists vnpt_dev.sla_metric_ratio_based;
drop TABLE if exists vnpt_dev.sla_metric_value_based;
drop TABLE if exists vnpt_dev.sla_templates;
drop TABLE if exists vnpt_dev.sla_template_metrics;
drop TABLE if exists vnpt_dev.sla_template_alarms;
drop TABLE if exists vnpt_dev.sla_policies;
CREATE TABLE vnpt_dev.sla_metric_groups (
                                            id              BIGSERIAL PRIMARY KEY,
                                            name            VARCHAR(100) NOT NULL UNIQUE,                     -- Tê<PERSON> nhóm chỉ số
                                            description     VARCHAR(300),                                     -- <PERSON><PERSON> tả chung về nhóm chỉ số
                                            status          int2,                               -- 1: ACTIVE, 0: INACTIVE
                                            created_by      BIGINT NOT NULL,                                  -- ID người tạo nhóm chỉ số
                                            created_at      TIMESTAMP,     -- Thời gian tạo
                                            modified_by     BIGINT,                                           -- ID người cập nhật
                                            modified_at     TIMESTAMP,                                         -- Thời gian cập nhật
                                            deleted_flag    int2
);

-- Ghi chú cho bảng và các cột (tùy chọn, giúp dễ hiểu trong pgAdmin)
COMMENT ON TABLE vnpt_dev.sla_metric_groups IS 'Quản lý danh sách các nhóm chỉ số của hệ thống';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.name IS 'Tên nhóm chỉ số';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.description IS 'Mô tả chung về nhóm chỉ số';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.status IS 'Trạng thái hoạt động của nhóm chỉ số (1: ACTIVE, 0: INACTIVE)';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.created_by IS 'ID người tạo nhóm chỉ số';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.created_at IS 'Thời gian tạo nhóm chỉ số';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.modified_by IS 'ID người cập nhật nhóm chỉ số';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.modified_at IS 'Thời gian cập nhật nhóm chỉ số';
COMMENT ON COLUMN vnpt_dev.sla_metric_groups.deleted_flag IS 'Trạng thái xóa (DELETED: 0, NOT_YET_DELETED: 1)';


-- Bảng: vnpt_dev.sla_attributes
-- Mô tả: Quản lý danh sách thuộc tính thuộc các nguồn dữ liệu cho tính năng SLA

CREATE TABLE vnpt_dev.sla_attributes (
                                         id              BIGSERIAL PRIMARY KEY,
                                         object_type     VARCHAR(50) NOT NULL,                          -- Nguồn dữ liệu (CUSTOMER_TICKET, ORDER, QUOTATION, WORK_ORDER)
                                         label           VARCHAR(250) NOT NULL,                         -- Tên hiển thị của thuộc tính
                                         name            VARCHAR(100) NOT NULL,                         -- Tên thuộc tính
                                         type            VARCHAR(50),                                   -- Loại dữ liệu (DATE, NUMBER, TEXT, ENUM)
                                         status          VARCHAR(20),                  -- Trạng thái (ACTIVE, INACTIVE)
                                         CONSTRAINT uq_sla_attributes_object_name UNIQUE (object_type, name)
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_attributes IS 'Quản lý danh sách thuộc tính thuộc các nguồn dữ liệu cho tính năng SLA';
COMMENT ON COLUMN vnpt_dev.sla_attributes.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_attributes.object_type IS 'Nguồn dữ liệu (CUSTOMER_TICKET, ORDER, QUOTATION, WORK_ORDER)';
COMMENT ON COLUMN vnpt_dev.sla_attributes.label IS 'Tên hiển thị của thuộc tính';
COMMENT ON COLUMN vnpt_dev.sla_attributes.name IS 'Tên thuộc tính (duy nhất trong cùng object_type)';
COMMENT ON COLUMN vnpt_dev.sla_attributes.type IS 'Loại dữ liệu của thuộc tính (DATE, NUMBER, TEXT, ENUM)';
COMMENT ON COLUMN vnpt_dev.sla_attributes.status IS 'Trạng thái thuộc tính (ACTIVE, INACTIVE)';


-- Bảng: vnpt_dev.sla_metrics
-- Mô tả: Quản lý danh sách các chỉ số (metric) của hệ thống

CREATE TABLE vnpt_dev.sla_metrics (
                                      id              BIGSERIAL PRIMARY KEY,
                                      label           VARCHAR(100) NOT NULL,                          -- Tên hiển thị của metric
                                      name            VARCHAR(100) NOT NULL UNIQUE,                   -- Định danh duy nhất của metric
                                      description     VARCHAR(1000) NOT NULL,                         -- Mô tả chi tiết về ý nghĩa và cách tính toán
                                      type            VARCHAR(50) NOT NULL,                           -- Loại chỉ số (TIME_BASED, RATIO_BASED, VALUE_BASED)
                                      group_ids       int8[],                                       -- Danh sách ID nhóm chỉ số (FK -> vnpt_dev.sla_metric_groups.id)
                                      object_type     VARCHAR(50) NOT NULL                            -- Nguồn dữ liệu (CUSTOMER_TICKET, ORDER, QUOTATION, WORK_ORDER)
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_metrics IS 'Quản lý danh sách các chỉ số (metric) của hệ thống';
COMMENT ON COLUMN vnpt_dev.sla_metrics.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_metrics.label IS 'Tên hiển thị của metric';
COMMENT ON COLUMN vnpt_dev.sla_metrics.name IS 'Tên metric (định danh duy nhất trong toàn hệ thống)';
COMMENT ON COLUMN vnpt_dev.sla_metrics.description IS 'Mô tả chi tiết về ý nghĩa, mục đích và cách tính toán của metric';
COMMENT ON COLUMN vnpt_dev.sla_metrics.type IS 'Loại chỉ số của metric (TIME_BASED, RATIO_BASED, VALUE_BASED)';
COMMENT ON COLUMN vnpt_dev.sla_metrics.group_ids IS 'Danh sách các ID nhóm chỉ số mà metric thuộc về (ID bảng vnpt_dev.sla_metric_groups)';
COMMENT ON COLUMN vnpt_dev.sla_metrics.object_type IS 'Nguồn dữ liệu (CUSTOMER_TICKET, ORDER, QUOTATION, WORK_ORDER)';


-- Bảng: vnpt_dev.sla_metric_time_based
-- Mô tả: Quản lý logic cho các metric đo lường dạng time-based

CREATE TABLE vnpt_dev.sla_metric_time_based (
                                                id                  BIGSERIAL PRIMARY KEY,
                                                metric_name         VARCHAR(100) NOT NULL,                      -- Tên metric (tham chiếu cột name trong bảng vnpt_dev.sla_metrics)
                                                start_cond          JSONB,                                      -- Logic bắt đầu tính thời gian (McConditionItemGroupDTO)
                                                end_cond            JSONB,                                      -- Logic kết thúc tính thời gian (McConditionItemGroupDTO)
                                                pause_cond          JSONB,                                      -- Logic tạm dừng tính thời gian (McConditionItemGroupDTO)
                                                start_cond_sql      TEXT,                                       -- SQL mô tả điều kiện bắt đầu tính thời gian
                                                end_cond_sql        TEXT,                                       -- SQL mô tả điều kiện kết thúc tính thời gian
                                                pause_cond_sql      TEXT
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_metric_time_based IS 'Quản lý logic cho các metric đo lường dạng time-based';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.metric_name IS 'Tên metric (cột name trong bảng vnpt_dev.sla_metrics)';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.start_cond IS 'Logic bắt đầu tính thời gian (McConditionItemGroupDTO)';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.end_cond IS 'Logic kết thúc tính thời gian (McConditionItemGroupDTO)';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.pause_cond IS 'Logic tạm dừng tính thời gian (McConditionItemGroupDTO)';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.start_cond_sql IS 'SQL mô tả điều kiện bắt đầu tính thời gian';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.end_cond_sql IS 'SQL mô tả điều kiện kết thúc tính thời gian';
COMMENT ON COLUMN vnpt_dev.sla_metric_time_based.pause_cond_sql IS 'SQL mô tả điều kiện tạm dừng tính thời gian';


-- Bảng: vnpt_dev.sla_metric_ratio_based
-- Mô tả: Quản lý logic cho các metric đo lường dạng ratio-based

CREATE TABLE vnpt_dev.sla_metric_ratio_based (
                                                 id                  BIGSERIAL PRIMARY KEY,
                                                 metric_name         VARCHAR(100) NOT NULL,                      -- Tên metric (cột name trong bảng vnpt_dev.sla_metrics)
                                                 success_cond        JSONB,                                      -- Điều kiện đánh giá kết quả thành công (McConditionItemGroupDTO)
                                                 total_cond          JSONB,                                      -- Điều kiện đánh giá tổng số lượng kết quả (McConditionItemGroupDTO)
                                                 success_cond_sql    TEXT,                                       -- SQL mô tả điều kiện kết quả thành công
                                                 total_cond_sql      TEXT,                                       -- SQL mô tả điều kiện tổng số lượng kết quả
                                                 start_date          DATE,                                       -- Ngày bắt đầu chu kỳ tính
                                                 end_date            DATE
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_metric_ratio_based IS 'Quản lý logic cho các metric đo lường dạng ratio-based';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.metric_name IS 'Tên metric (cột name trong bảng vnpt_dev.sla_metrics)';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.success_cond IS 'Điều kiện đánh giá kết quả thành công (McConditionItemGroupDTO)';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.total_cond IS 'Điều kiện đánh giá tổng số lượng kết quả (McConditionItemGroupDTO)';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.success_cond_sql IS 'SQL mô tả điều kiện kết quả thành công';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.total_cond_sql IS 'SQL mô tả điều kiện tổng số lượng kết quả';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.start_date IS 'Ngày bắt đầu chu kỳ tính';
COMMENT ON COLUMN vnpt_dev.sla_metric_ratio_based.end_date IS 'Ngày kết thúc chu kỳ tính';


-- Bảng: vnpt_dev.sla_metric_value_based
-- Mô tả: Quản lý logic cho các metric đo lường dạng value-based

CREATE TABLE vnpt_dev.sla_metric_value_based (
                                                 id                  BIGSERIAL PRIMARY KEY,
                                                 metric_name         VARCHAR(100) NOT NULL,                      -- Tên metric (cột name trong bảng vnpt_dev.sla_metrics)
                                                 activation_cond     JSONB,                                      -- Logic kích hoạt việc lấy giá trị tham số
                                                 activation_cond_sql TEXT,                                       -- SQL mô tả điều kiện kích hoạt việc lấy giá trị của tham số
                                                 attribute_id        BIGINT
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_metric_value_based IS 'Quản lý logic cho các metric đo lường dạng value-based';
COMMENT ON COLUMN vnpt_dev.sla_metric_value_based.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_metric_value_based.metric_name IS 'Tên metric (cột name trong bảng vnpt_dev.sla_metrics)';
COMMENT ON COLUMN vnpt_dev.sla_metric_value_based.activation_cond IS 'Logic kích hoạt việc lấy giá trị tham số (McConditionItemGroupDTO)';
COMMENT ON COLUMN vnpt_dev.sla_metric_value_based.activation_cond_sql IS 'SQL mô tả điều kiện kích hoạt việc lấy giá trị của tham số';
COMMENT ON COLUMN vnpt_dev.sla_metric_value_based.attribute_id IS 'ID thuộc tính cần ghi nhận dữ liệu (ID bảng vnpt_dev.sla_attributes)';

-- Bảng: vnpt_dev.sla_templates
-- Mô tả: Quản lý các mẫu cam kết (SLA) của hệ thống

CREATE TABLE vnpt_dev.sla_templates (
                                        id              BIGSERIAL PRIMARY KEY,
                                        name            VARCHAR(100) NOT NULL,                          -- Tên mẫu cam kết
                                        code            VARCHAR(100) NOT NULL UNIQUE,                   -- Mã mẫu cam kết
                                        description     VARCHAR(500) NOT NULL,                          -- Mô tả mục đích, phạm vi áp dụng, đối tượng hướng tới
                                        type            VARCHAR(50),                                   -- Loại cam kết (CUSTOMER, PARTNER, OLA, UC)
                                        status          SMALLINT DEFAULT 1,                             -- 1: ACTIVE, 0: INACTIVE
                                        created_by      BIGINT NOT NULL,                                -- ID người tạo
                                        created_at      TIMESTAMP ,   -- Thời gian tạo
                                        modified_by     BIGINT,                                         -- ID người cập nhật
                                        modified_at     TIMESTAMP                                       -- Thời gian cập nhật
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_templates IS 'Quản lý các mẫu cam kết (SLA) của hệ thống';
COMMENT ON COLUMN vnpt_dev.sla_templates.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_templates.name IS 'Tên mẫu cam kết';
COMMENT ON COLUMN vnpt_dev.sla_templates.code IS 'Mã mẫu cam kết (duy nhất)';
COMMENT ON COLUMN vnpt_dev.sla_templates.description IS 'Mô tả mục đích, phạm vi áp dụng và đối tượng hướng tới';
COMMENT ON COLUMN vnpt_dev.sla_templates.type IS 'Loại cam kết (CUSTOMER, PARTNER, OLA, UC)';
COMMENT ON COLUMN vnpt_dev.sla_templates.status IS 'Trạng thái hoạt động của mẫu cam kết (1: ACTIVE, 0: INACTIVE)';
COMMENT ON COLUMN vnpt_dev.sla_templates.created_by IS 'ID người tạo mẫu cam kết';
COMMENT ON COLUMN vnpt_dev.sla_templates.created_at IS 'Thời gian tạo mẫu cam kết';
COMMENT ON COLUMN vnpt_dev.sla_templates.modified_by IS 'ID người cập nhật mẫu cam kết';
COMMENT ON COLUMN vnpt_dev.sla_templates.modified_at IS 'Thời gian cập nhật mẫu cam kết';



CREATE TABLE vnpt_dev.sla_template_metrics (
                                               id BIGSERIAL PRIMARY KEY,
                                               sla_template_id int8 NOT NULL,
                                               metric_id int8 NOT NULL,
                                               target_threshold JSONB,
                                               warning_threshold JSONB,
                                               breach_threshold JSONB,
                                               working_time VARCHAR(50)
);

-- Ghi chú cho bảng và các cột
COMMENT ON TABLE vnpt_dev.sla_template_metrics IS 'Quản lý danh sách metrics ứng với các mẫu cam kết (SLA template)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.sla_template_id IS 'ID mẫu cam kết (ID bảng sla_templates)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.metric_id IS 'ID metric (ID trong bảng sla_metrics)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.target_threshold IS 'Ngưỡng mục tiêu của metric trong SLA template (Cấu trúc ngưỡng metric)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.warning_threshold IS 'Ngưỡng cảnh báo của metric trong SLA template (Cấu trúc ngưỡng metric)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.breach_threshold IS 'Ngưỡng vi phạm của metric trong SLA template (Cấu trúc ngưỡng metric)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.working_time IS 'Cấu hình lịch làm việc (OFFICE_HOURS, SUPPORT_247) - áp dụng cho metric dạng time-based';


CREATE TABLE vnpt_dev.sla_template_alarms (
                                              id BIGSERIAL PRIMARY KEY,
                                              name VARCHAR(100) NOT NULL,
                                              sla_template_metric_id BIGINT NOT NULL,
                                              threshold_type VARCHAR(50) NOT NULL,  -- TARGET, WARNING, BREACH
                                              action VARCHAR(50) NOT NULL,          -- NOTIFY, UPDATE, ASSIGN
                                              notification_id BIGINT,               -- áp dụng khi action = NOTIFY
                                              attribute_id BIGINT,                  -- áp dụng khi action = UPDATE
                                              attribute_value JSONB,                -- áp dụng khi action = UPDATE
                                              assignee_id BIGINT
);

-- ===========================================================
-- COMMENT
-- ===========================================================

COMMENT ON TABLE vnpt_dev.sla_template_alarms IS 'Thiết lập cảnh báo tương ứng với mẫu cam kết (SLA Template)';

COMMENT ON COLUMN vnpt_dev.sla_template_alarms.id IS 'id định danh duy nhất cho từng quy tắc cảnh báo';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.name IS 'Tên quy tắc cảnh báo';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.sla_template_metric_id IS 'ID metric trong mẫu cam kết (ID bảng vnpt_dev.sla_template_metrics)';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.threshold_type IS 'Loại ngưỡng cảnh báo (TARGET, WARNING, BREACH)';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.action IS 'Loại hành động cảnh báo (NOTIFY, UPDATE, ASSIGN)';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.notification_id IS 'ID cấu hình thông báo (ID bảng task_notifications) — áp dụng khi action = NOTIFY';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.attribute_id IS 'ID thuộc tính cần cập nhật dữ liệu (ID bảng vnpt_dev.sla_attributes) — áp dụng khi action = UPDATE';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.attribute_value IS 'Giá trị thuộc tính cần cập nhật — áp dụng khi action = UPDATE';
COMMENT ON COLUMN vnpt_dev.sla_template_alarms.assignee_id IS 'ID nhân sự cần gán (ID bảng users) — áp dụng khi action = ASSIGN';

CREATE TABLE vnpt_dev.sla_policies (
                                       id BIGSERIAL PRIMARY KEY,
                                       name VARCHAR(100) NOT NULL UNIQUE,
                                       sla_template_id BIGINT NOT NULL,
                                       description VARCHAR(500) ,
                                       apply_cond JSONB,
                                       apply_cond_sql TEXT,
                                       priority INT2,
                                       status INT2,
                                       created_by BIGINT NOT NULL,
                                       created_at TIMESTAMP,
                                       modified_by BIGINT,
                                       modified_at TIMESTAMP,
                                       deleted_flag    int2
);

-- ===========================================================
-- COMMENT
-- ===========================================================

COMMENT ON TABLE vnpt_dev.sla_policies IS 'Quản lý chính sách SLA (SLA Policies)';

COMMENT ON COLUMN vnpt_dev.sla_policies.id IS 'id';
COMMENT ON COLUMN vnpt_dev.sla_policies.name IS 'Tên chính sách SLA — duy nhất trong hệ thống';
COMMENT ON COLUMN vnpt_dev.sla_policies.sla_template_id IS 'ID mẫu cam kết (ID bảng vnpt_dev.sla_templates)';
COMMENT ON COLUMN vnpt_dev.sla_policies.apply_cond IS 'Điều kiện áp dụng chính sách SLA (định nghĩa logic ở dạng JSON)';
COMMENT ON COLUMN vnpt_dev.sla_policies.apply_cond_sql IS 'SQL mô tả điều kiện áp dụng chính sách SLA';
COMMENT ON COLUMN vnpt_dev.sla_policies.priority IS 'Độ ưu tiên của chính sách SLA (0: cao nhất, 16535: thấp nhất)';
COMMENT ON COLUMN vnpt_dev.sla_policies.status IS 'Trạng thái hoạt động của chính sách SLA (1: ACTIVE, 0: INACTIVE)';
COMMENT ON COLUMN vnpt_dev.sla_policies.created_by IS 'ID người tạo chính sách SLA';
COMMENT ON COLUMN vnpt_dev.sla_policies.created_at IS 'Thời gian tạo chính sách SLA';
COMMENT ON COLUMN vnpt_dev.sla_policies.modified_by IS 'ID người cập nhật chính sách SLA';
COMMENT ON COLUMN vnpt_dev.sla_policies.modified_at IS 'Thời gian cập nhật chính sách SLA';
COMMENT ON COLUMN vnpt_dev.sla_policies.deleted_flag IS 'Trạng thái xóa (DELETED: 0, NOT_YET_DELETED: 1)';
COMMENT ON COLUMN vnpt_dev.sla_policies.description IS 'Mô tả chính sách';


delete from vnpt_dev.sla_attributes;
INSERT INTO sla_attributes (object_type, label, name, type, status)
VALUES
-- Ticket
('CUSTOMER_TICKET', 'Trạng thái', 'status', 'NUMBER', 'ACTIVE'),
('CUSTOMER_TICKET', 'Mức độ ưu tiên', 'priority', 'NUMBER', 'ACTIVE'),
('CUSTOMER_TICKET', 'Loại phiếu hỗ trợ', 'support_type', 'NUMBER', 'ACTIVE'),
('CUSTOMER_TICKET', 'Nguồn tạo', 'created_source', 'NUMBER', 'ACTIVE'),
('CUSTOMER_TICKET', 'Đối tượng khách hàng', 'customer_type', 'String', 'ACTIVE'),
('CUSTOMER_TICKET', 'Tỉnh/thành phố', 'province_id', 'NUMBER', 'ACTIVE'),
('CUSTOMER_TICKET', 'Phân vùng khách hàng', 'partition_name', 'TEXT', 'ACTIVE'),

-- Order
('ORDER', 'Trạng thái', 'name', 'TEXT', 'ACTIVE'),
('ORDER', 'Loại Đơn hàng', 'product_types', 'TEXT', 'ACTIVE'),
('ORDER', 'Trạng thái thanh toán', 'status', 'ENUM', 'ACTIVE'),
('ORDER', 'Phương thức thanh toán', 'payment_method', 'ENUM', 'ACTIVE'),
('ORDER', 'Danh mục sản phẩm', 'categories_ids', 'TEXT', 'ACTIVE'),
('ORDER', 'Đối tượng khách hàng', 'customer_type', 'TEXT', 'ACTIVE'),
('ORDER', 'Tỉnh / thành phố giao hàng', 'shipping_province_id', 'NUMBER', 'ACTIVE'),
('ORDER', 'Phường / xã giao hàng', 'shipping_district_id', 'NUMBER', 'ACTIVE'),
('ORDER', 'Nhà cung cấp', 'provider_id', 'NUMBER', 'ACTIVE'),
('ORDER', 'Tên sản phẩm', 'sku', 'TEXT', 'ACTIVE'),
('ORDER', 'Kho hàng', 'warehouse', 'TEXT', 'ACTIVE'),
('ORDER', 'Trạng thái kho', 'warehouse_status', 'ENUM', 'ACTIVE'),

-- Shipping (chưa phát triển)
('SHIPPING', 'Trạng thái', 'shipping_status', 'ENUM', 'ACTIVE'),
('SHIPPING', 'Đối tác vận chuyển', 'shipping_partner', 'TEXT', 'ACTIVE'),
('SHIPPING', 'Khu vực giao', 'shipping_region', 'TEXT', 'ACTIVE'),
('SHIPPING', 'Trạng thái chờ', 'shipping_wait_status', 'ENUM', 'ACTIVE'),
('SHIPPING', 'Ngày giao dự kiến', 'expected_delivery_date', 'DATE', 'ACTIVE'),
('SHIPPING', 'Ngày giao thực tế', 'actual_delivery_date', 'DATE', 'ACTIVE'),

-- Work Order
('WORK_ORDER', 'Trạng thái công việc', 'state_id', 'ENUM', 'ACTIVE'),
('WORK_ORDER', 'Loại công việc', 'type_id', 'NUMBER', 'ACTIVE'),
('WORK_ORDER', 'Mức độ ưu tiên', 'priority', 'NUMBER', 'ACTIVE');


-- Thêm operand
delete from vnpt_dev.mc_operand where name in ('Trạng thái của công việc', 'Loại công việc', 'Độ ưu tiên của công việc');
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status")
VALUES ('Trạng thái của công việc', 10007, 1, 26, NULL, NULL, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status")
VALUES ('Loại công việc', 10008, 1, 26, NULL, NULL, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status")
VALUES ('Độ ưu tiên của công việc', 10009, 1, 18, NULL, NULL, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL);

delete from vnpt_dev.mc_operand_operator_mapping where operand_data_type_code in (18,26) and value_data_type_code IN (18,16);
INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ("operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (18, 1, 18, '2022-06-09 08:07:14.178', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ( "operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (18, 2, 18, '2022-06-09 08:07:14.178', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ("operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (26, 1, 26, '2022-06-09 08:07:14.178', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ( "operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (26, 2, 26, '2022-06-09 08:07:14.178', NULL, NULL, NULL, NULL, NULL);