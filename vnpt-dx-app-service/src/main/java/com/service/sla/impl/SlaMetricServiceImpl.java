package com.service.sla.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.dto.sla.IGetSlaMetricGroupDTO;
import com.dto.sla.SlaMetricGroupCreateDTO;
import com.dto.sla.request.SlaMetricCreateRequestDTO;
import com.dto.sla.request.SlaMetricDeleteReqDTO;
import com.dto.sla.request.SlaMetricUpdateRequestDTO;
import com.dto.sla.request.SlaMetricValidateRequestDTO;
import com.dto.sla.response.SlaMetricDeleteResDTO;
import com.dto.sla.response.SlaMetricNameResponseDTO;
import com.dto.sla.response.SlaMetricResponseDTO;
import com.entity.sla.SlaMetric;
import com.entity.sla.SlaMetricGroup;
import com.entity.sla.SlaMetricRatioBased;
import com.entity.sla.SlaMetricTimeBased;
import com.entity.sla.SlaMetricValueBased;
import com.enums.sla.SlaMetricTypeEnum;
import com.enums.sla.SlaSourceTypeEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.repository.sla.SlaMetricGroupRepository;
import com.repository.sla.SlaMetricRatioBasedRepository;
import com.repository.sla.SlaMetricRepository;
import com.repository.sla.SlaMetricTimeBasedRepository;
import com.repository.sla.SlaMetricValueBasedRepository;
import com.dto.common.ICommonIdName;
import com.dto.sla.IGetListMetric;
import com.enums.sla.SlaObjectTypeEnum;
import com.onedx.common.utils.SqlUtils;
import com.repository.sla.SlaTemplateMetricRepository;
import com.repository.sla.SlaTemplateMetricRepository;
import com.service.sla.SlaMetricService;
import com.service.utils.condition.ConditionExecute;
import com.util.AuthUtil;
import com.util.SlaMetricUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
public class SlaMetricServiceImpl implements SlaMetricService {

    private final SlaMetricGroupRepository slaMetricGroupRepository;
    private final ExceptionFactory exceptionFactory;
    private final SlaMetricRepository slaMetricRepository;
    private final SlaMetricTimeBasedRepository slaMetricTimeBasedRepository;
    private final SlaMetricRatioBasedRepository slaMetricRatioBasedRepository;
    private final SlaMetricValueBasedRepository slaMetricValueBasedRepository;
    private final SlaTemplateMetricRepository slaTemplateMetricRepository;


    // Constants cho code generation
    private static final String CODE_PREFIX = "MT";
    private static final int CODE_NUMBER_LENGTH = 6;

    @Override
    @Transactional
    public Long createMetricGroup(SlaMetricGroupCreateDTO dto) {
        validateNameMetricGroup(dto.getName(), dto.getId());
        Long currentId = AuthUtil.getCurrentUserId();
        SlaMetricGroup metricGroup = SlaMetricGroup.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .status(dto.getStatus().value)
                .build();
        metricGroup.setCreatedBy(currentId);
        metricGroup.setModifiedBy(currentId);
        metricGroup.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());

        SlaMetricGroup save = slaMetricGroupRepository.save(metricGroup);
        return save.getId();
    }

    /**
     * Cập nhật nhóm chỉ số
     */
    @Override
    @Transactional
    public Long updateMetricGroup(SlaMetricGroupCreateDTO dto) {
        validateNameMetricGroup(dto.getName(), dto.getId());
        Long currentId = AuthUtil.getCurrentUserId();
        SlaMetricGroup metricGroup = getSlaMetricGroup(dto.getId());
        metricGroup.setName(dto.getName());
        metricGroup.setDescription(dto.getDescription());
        metricGroup.setStatus(dto.getStatus().value);
        metricGroup.setModifiedBy(currentId);

        SlaMetricGroup save = slaMetricGroupRepository.save(metricGroup);
        return save.getId();
    }

    /**
     * Lấy danh sách người tạo metric group
     */
    @Override
    public List<ICommonIdName> getCreatedByMetricGroup(String name) {
        return slaMetricGroupRepository.getListCreatedByMetricGroup(name);
    }

    /**
     * Danh sách nhóm chỉ số
     */
    @Override
    public Page<IGetSlaMetricGroupDTO> getMetricGroup(Integer isName, String value, StatusEnum status, Long createdBy, Pageable pageable) {
        return slaMetricGroupRepository.getMetricGroup(isName, value, status.value, createdBy, pageable);
    }

    /**
     * check trung ten metric group
     */
    @Override
    public void validateNameMetricGroup(String name, Long id) {
        Long metricGroupId = Objects.nonNull(id) ? id : -1L;
        if(slaMetricGroupRepository.existsByNameAndDeletedFlagAndIdNot(name, DeletedFlag.NOT_YET_DELETED.getValue(), metricGroupId)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.Sla.METRIC_GROUP_NAME_EXIST, ErrorKey.SLA_METRIC_GROUP,
                    ErrorKey.SlaMetric.NAME);
        }
    }

    /**
     * Xóa nhóm chỉ số
     */
    @Override
    public void deleteMetricGroup(Long id) {
        SlaMetricGroup metricGroup = getSlaMetricGroup(id);
        metricGroup.setDeletedFlag(DeletedFlag.DELETED.getValue());
        slaMetricGroupRepository.save(metricGroup);
    }

    private SlaMetricGroup getSlaMetricGroup(Long id) {
        SlaMetricGroup metricGroup = slaMetricGroupRepository.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        if(Objects.isNull(metricGroup)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.Sla.METRIC_GROUP_NOT_FOUND, ErrorKey.SLA_METRIC_GROUP,
                    ErrorKey.SlaMetric.ID);
        }
        return metricGroup;
    }


    @Override
    @Transactional
    public SlaMetricResponseDTO createSlaMetric(SlaMetricCreateRequestDTO request) {
        log.info("createSlaMetric: start with request {}", request);
        // Validate tên hiển thị không trùng
        if (slaMetricRepository.existsByLabel(request.getLabel())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.SlaMetric.LABEL_ALREADY_EXISTS,
                Resources.SLA_METRIC, ErrorKey.SlaMetric.LABEL);
        }

        // Tạo mã metric tự động
        String code = generateUniqueCode();

        // Tạo SlaMetric chính
        SlaMetric slaMetric = new SlaMetric();
        slaMetric.setCode(code);
        slaMetric.setLabel(request.getLabel());
        slaMetric.setName(request.getName());
        slaMetric.setDescription(request.getDescription());
        slaMetric.setType(request.getType());
        slaMetric.setSourceType(SlaSourceTypeEnum.CUSTOM);
        slaMetric.setGroupIds(request.getGroupIds());
        slaMetric.setObjectType(request.getObjectType());

        // Set audit fields
        slaMetric.setStatus(1);
        slaMetric.setCreatedBy(AuthUtil.getCurrentUserId());
        slaMetric.setCreatedAt(LocalDateTime.now());
        slaMetric.setDeletedFlag(1);

        SlaMetric savedMetric = slaMetricRepository.save(slaMetric);

        // Tạo bảng con tương ứng với loại metric
        createMetricTypeSpecificEntity(savedMetric, request);

        log.info("createSlaMetric: end with id {}", savedMetric.getId());
        return buildResponseDTO(savedMetric);
    }

    @Override
    @Transactional
    public SlaMetricResponseDTO updateSlaMetric(SlaMetricUpdateRequestDTO request) {
        log.info("updateSlaMetric: start with request {}", request);

        // Tìm metric hiện tại
        SlaMetric existingMetric = slaMetricRepository.findById(request.getId())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(MessageKeyConstant.NOT_FOUND,
                Resources.SLA_METRIC, ErrorKey.SlaMetric.ID, String.valueOf(request.getId())));

        // Validate tên hiển thị không trùng (trừ chính nó)
        if (slaMetricRepository.existsByLabelAndIdNot(request.getLabel(), request.getId())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.SlaMetric.LABEL_ALREADY_EXISTS,
                Resources.SLA_METRIC, ErrorKey.SlaMetric.LABEL);
        }

        // Cập nhật thông tin cơ bản
        existingMetric.setLabel(request.getLabel());
        existingMetric.setDescription(request.getDescription());
        existingMetric.setType(request.getType());
        existingMetric.setGroupIds(request.getGroupIds());
        existingMetric.setObjectType(request.getObjectType());

        // Set modified audit fields
        existingMetric.setModifiedBy(AuthUtil.getCurrentUserId());
        existingMetric.setModifiedAt(LocalDateTime.now());

        SlaMetric savedMetric = slaMetricRepository.save(existingMetric);

        // Xóa và tạo lại bảng con nếu loại metric thay đổi
        deleteMetricTypeSpecificEntity(savedMetric);
        createMetricTypeSpecificEntity(savedMetric, convertUpdateToCreate(request));

        log.info("updateSlaMetric: end with id {}", savedMetric.getId());
        return buildResponseDTO(savedMetric);
    }
    /**
     * Tạo entity con tương ứng với loại metric
     */
    private void createMetricTypeSpecificEntity(SlaMetric slaMetric, SlaMetricCreateRequestDTO request) {
        switch (slaMetric.getType()) {
            case TIME_BASED:
                createTimeBasedEntity(slaMetric, request);
                break;
            case RATIO_BASED:
                createRatioBasedEntity(slaMetric, request);
                break;
            case VALUE_BASED:
                createValueBasedEntity(slaMetric, request);
                break;
        }
    }

    /**
     * Tạo SlaMetricTimeBased entity
     */
    private void createTimeBasedEntity(SlaMetric slaMetric, SlaMetricCreateRequestDTO request) {
        SlaMetricTimeBased timeBased = new SlaMetricTimeBased();
        timeBased.setMetricId(slaMetric.getId());
        timeBased.setStartCond(request.getStartCond());
        timeBased.setEndCond(request.getEndCond());
        timeBased.setPauseCond(request.getPauseCond());

        // Tạo SQL từ condition
        if (request.getStartCond() != null) {
            ConditionExecute startCondExecute = new ConditionExecute(request.getStartCond());
            String startCondSql = startCondExecute.getCondition(slaMetric.getObjectType().getValue());
            timeBased.setStartCondSql(startCondSql);
        }

        if (request.getEndCond() != null) {
            ConditionExecute endCondExecute = new ConditionExecute(request.getEndCond());
            String endCondSql = endCondExecute.getCondition(slaMetric.getObjectType().getValue());
            timeBased.setEndCondSql(endCondSql);
        }

        if (request.getPauseCond() != null) {
            ConditionExecute pauseCondExecute = new ConditionExecute(request.getPauseCond());
            String pauseCondSql = pauseCondExecute.getCondition(slaMetric.getObjectType().getValue());
            timeBased.setPauseCondSql(pauseCondSql);
        }

        slaMetricTimeBasedRepository.save(timeBased);
    }

    /**
     * Tạo SlaMetricRatioBased entity
     */
    private void createRatioBasedEntity(SlaMetric slaMetric, SlaMetricCreateRequestDTO request) {
        SlaMetricRatioBased ratioBased = new SlaMetricRatioBased();
        ratioBased.setMetricId(slaMetric.getId());
        ratioBased.setSuccessCond(request.getSuccessCond());
        ratioBased.setTotalCond(request.getTotalCond());
        ratioBased.setMeasurementWindow(request.getMeasurementWindow());

        // Tạo SQL từ condition
        if (request.getSuccessCond() != null) {
            ConditionExecute successCondExecute = new ConditionExecute(request.getSuccessCond());
            String successCondSql = successCondExecute.getCondition(slaMetric.getObjectType().getValue());
            ratioBased.setSuccessCondSql(successCondSql);
        }

        if (request.getTotalCond() != null) {
            ConditionExecute totalCondExecute = new ConditionExecute(request.getTotalCond());
            String totalCondSql = totalCondExecute.getCondition(slaMetric.getObjectType().getValue());
            ratioBased.setTotalCondSql(totalCondSql);
        }
        slaMetricRatioBasedRepository.save(ratioBased);
    }

    /**
     * Tạo SlaMetricValueBased entity
     */
    private void createValueBasedEntity(SlaMetric slaMetric, SlaMetricCreateRequestDTO request) {
        SlaMetricValueBased valueBased = new SlaMetricValueBased();
        valueBased.setMetricId(slaMetric.getId());
        valueBased.setActivationCond(request.getActivationCond());
        valueBased.setAttributeId(request.getAttributeId());

        // Tạo SQL từ condition
        if (request.getActivationCond() != null) {
            ConditionExecute activationCondExecute = new ConditionExecute(request.getActivationCond());
            String activationCondSql = activationCondExecute.getCondition(slaMetric.getObjectType().getValue());
            valueBased.setActivationCondSql(activationCondSql);
        }
        slaMetricValueBasedRepository.save(valueBased);
    }

    /**
     * Xóa entity con tương ứng với loại metric
     */
    private void deleteMetricTypeSpecificEntity(SlaMetric slaMetric) {
        // Xóa tất cả các bảng con có thể có bằng metric ID
        slaMetricTimeBasedRepository.deleteByMetricId(slaMetric.getId());
        slaMetricRatioBasedRepository.deleteByMetricId(slaMetric.getId());
        slaMetricValueBasedRepository.deleteByMetricId(slaMetric.getId());
    }

    /**
     * Chuyển đổi UpdateRequestDTO thành CreateRequestDTO
     */
    private SlaMetricCreateRequestDTO convertUpdateToCreate(SlaMetricUpdateRequestDTO updateRequest) {
        SlaMetricCreateRequestDTO createRequest = new SlaMetricCreateRequestDTO();
        BeanUtils.copyProperties(updateRequest, createRequest);
        return createRequest;
    }

    /**
     * Xây dựng response DTO từ entity
     */
    private SlaMetricResponseDTO buildResponseDTO(SlaMetric slaMetric) {
        SlaMetricResponseDTO response = SlaMetricResponseDTO.builder()
            .id(slaMetric.getId())
            .code(slaMetric.getCode())
            .label(slaMetric.getLabel())
            .name(slaMetric.getName())
            .description(slaMetric.getDescription())
            .type(slaMetric.getType())
            .groupIds(slaMetric.getGroupIds())
            .objectType(slaMetric.getObjectType())
            // Audit fields
            .status(slaMetric.getStatus())
            .createdBy(slaMetric.getCreatedBy())
            .createdAt(slaMetric.getCreatedAt())
            .modifiedBy(slaMetric.getModifiedBy())
            .modifiedAt(slaMetric.getModifiedAt())
            .deletedFlag(slaMetric.getDeletedFlag())
            .build();

        // Lấy thông tin từ bảng con tương ứng
        switch (slaMetric.getType()) {
            case TIME_BASED:
                populateTimeBasedData(response, slaMetric.getId());
                break;
            case RATIO_BASED:
                populateRatioBasedData(response, slaMetric.getId());
                break;
            case VALUE_BASED:
                populateValueBasedData(response, slaMetric.getId());
                break;
        }

        return response;
    }

    /**
     * Điền dữ liệu TIME_BASED vào response
     */
    private void populateTimeBasedData(SlaMetricResponseDTO response, Long metricId) {
        Optional<SlaMetricTimeBased> timeBasedOpt = slaMetricTimeBasedRepository.findByMetricId(metricId);
        if (timeBasedOpt.isPresent()) {
            SlaMetricTimeBased timeBased = timeBasedOpt.get();
            response.setStartCond(timeBased.getStartCond());
            response.setEndCond(timeBased.getEndCond());
            response.setPauseCond(timeBased.getPauseCond());
            response.setStartCondSql(timeBased.getStartCondSql());
            response.setEndCondSql(timeBased.getEndCondSql());
            response.setPauseCondSql(timeBased.getPauseCondSql());
        }
    }

    /**
     * Điền dữ liệu RATIO_BASED vào response
     */
    private void populateRatioBasedData(SlaMetricResponseDTO response, Long metricId) {
        Optional<SlaMetricRatioBased> ratioBasedOpt = slaMetricRatioBasedRepository.findByMetricId(metricId);
        if (ratioBasedOpt.isPresent()) {
            SlaMetricRatioBased ratioBased = ratioBasedOpt.get();
            response.setSuccessCond(ratioBased.getSuccessCond());
            response.setTotalCond(ratioBased.getTotalCond());
            response.setSuccessCondSql(ratioBased.getSuccessCondSql());
            response.setTotalCondSql(ratioBased.getTotalCondSql());
            response.setMeasurementWindow(ratioBased.getMeasurementWindow());
        }
    }

    /**
     * Điền dữ liệu VALUE_BASED vào response
     */
    private void populateValueBasedData(SlaMetricResponseDTO response, Long metricId) {
        Optional<SlaMetricValueBased> valueBasedOpt = slaMetricValueBasedRepository.findByMetricId(metricId);
        if (valueBasedOpt.isPresent()) {
            SlaMetricValueBased valueBased = valueBasedOpt.get();
            response.setActivationCond(valueBased.getActivationCond());
            response.setActivationCondSql(valueBased.getActivationCondSql());
            response.setAttributeId(valueBased.getAttributeId());
        }
    }

    /**
     * Generate mã metric unique bằng cách tìm max number và cộng thêm 1
     * Format: MTxxxxxx (MT + 6 số tăng dần)
     *
     * @return String mã metric unique
     */
    private String generateUniqueCode() {
        log.debug("generateUniqueCode: start");

        try {
            // Lấy số lớn nhất hiện tại từ database
            Integer maxNumber = slaMetricRepository.getMaxCodeNumber();

            // Cộng thêm 1 để tạo số mới
            long nextNumber = (maxNumber != null ? maxNumber : 0) + 1;

            String code = generateCodeFromNumber(nextNumber);

            // Double check để đảm bảo code không trùng (trong trường hợp concurrent)
            while (slaMetricRepository.existsByCode(code)) {
                nextNumber++;
                code = generateCodeFromNumber(nextNumber);
            }

            log.debug("generateUniqueCode: Generated code {} from number {}", code, nextNumber);
            return code;

        } catch (Exception e) {
            log.error("generateUniqueCode: Error generating code", e);
            throw new RuntimeException("Không thể tạo mã metric unique", e);
        }
    }

    /**
     * Generate code từ số number
     * Format: MTxxxxxx (MT + 6 số tăng dần)
     *
     * @param number số để tạo code
     * @return String mã metric
     */
    private String generateCodeFromNumber(long number) {
        // Đảm bảo số không vượt quá 6 chữ số (999999)
        long validNumber = number % 1000000;

        // Format với leading zeros
        String numberStr = String.format("%0" + CODE_NUMBER_LENGTH + "d", validNumber);

        return CODE_PREFIX + numberStr;
    }


    @Override
    public void deleteSlaMetric(Long id) {
        log.info("deleteSlaMetric: start with id {}", id);

        SlaMetric slaMetric = slaMetricRepository.findById(id)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(MessageKeyConstant.NOT_FOUND,
                Resources.SLA_METRIC, ErrorKey.SlaMetric.ID));

        // Xóa các bảng con trước
        deleteMetricTypeSpecificEntity(slaMetric);

        // Xóa SLA Metric chính
        slaMetricRepository.delete(slaMetric);

        log.info("deleteSlaMetric: end");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SlaMetricDeleteResDTO deleteSlaMetrics(SlaMetricDeleteReqDTO request) {
        log.info("deleteSlaMetrics: start with {} ids", request.getIds().size());

        Set<Long> ids = request.getIds();

        // Tìm các metric tồn tại
        List<SlaMetric> existingMetrics = slaMetricRepository.findAllById(ids);

        // Kiểm tra tất cả IDs có tồn tại không
        if (existingMetrics.size() != ids.size()) {
            Set<Long> foundIds = existingMetrics.stream()
                    .map(SlaMetric::getId)
                    .collect(Collectors.toSet());
            Set<Long> notFoundIds = new HashSet<>(ids);
            notFoundIds.removeAll(foundIds);
            throw exceptionFactory.badRequest("Không tìm thấy SLA Metric với IDs: " + notFoundIds,
                    Resources.SLA_METRIC, ErrorKey.SlaMetric.ID);
        }

        // Kiểm tra có metric nào đang được sử dụng trong template không
        List<Long> usedMetricIds = new ArrayList<>();
        for (SlaMetric metric : existingMetrics) {
            if (slaTemplateMetricRepository.existsByMetricId(metric.getId())) {
                usedMetricIds.add(metric.getId());
            }
        }

        // Nếu có metric đang được sử dụng thì throw exception để rollback
        if (!usedMetricIds.isEmpty()) {
            throw exceptionFactory.badRequest("Không thể xóa SLA Metric đang được sử dụng trong template. IDs: " + usedMetricIds,
                    Resources.SLA_METRIC, ErrorKey.SlaMetric.ID);
        }

        // Xóa tất cả metric
        for (SlaMetric metric : existingMetrics) {
            // Xóa các bảng con trước
            deleteMetricTypeSpecificEntity(metric);
            // Xóa SLA Metric chính
            slaMetricRepository.delete(metric);
        }

        // Tạo response
        SlaMetricDeleteResDTO response = new SlaMetricDeleteResDTO();
        response.setCode("SUCCESS");
        response.setMessage("Xóa SLA Metric thành công");

        log.info("deleteSlaMetrics: end - Đã xóa {} metrics", existingMetrics.size());
        return response;
    }

    @Override
    public SlaMetricNameResponseDTO validateAndGenerateName(SlaMetricValidateRequestDTO request) {
        log.info("validateAndGenerateName: start with label {} and id {}", request.getLabel(), request.getId());

        // Kiểm tra label có trùng không (exclude ID hiện tại nếu có)
        boolean isLabelExists;
        if (request.getId() != null) {
            // Trường hợp update: check trùng exclude ID hiện tại
            isLabelExists = slaMetricRepository.existsByLabelAndIdNot(request.getLabel(), request.getId());
        } else {
            // Trường hợp create: check trùng bình thường
            isLabelExists = slaMetricRepository.existsByLabel(request.getLabel());
        }

        if (isLabelExists) {
            return SlaMetricNameResponseDTO.builder()
                .isValid(false)
                .message("Label đã tồn tại trong hệ thống")
                .build();
        }

        Set<String> existingNames = slaMetricRepository.findAllSystemNames();

        // Nếu là update, loại bỏ system name của metric hiện tại
        if (request.getId() != null) {
            Optional<SlaMetric> currentMetric = slaMetricRepository.findById(request.getId());
            if (currentMetric.isPresent() && currentMetric.get().getName() != null) {
                existingNames.remove(currentMetric.get().getName());
            }
        }

        String systemName = SlaMetricUtil.generateUniqueSystemName(request.getLabel(), existingNames);

        SlaMetricNameResponseDTO response = SlaMetricNameResponseDTO.builder()
            .systemName(systemName)
            .isValid(true)
            .message("Label hợp lệ")
            .build();

        return response;
    }




    @Override
    public Page<IGetListMetric> getListMetric(Integer isMetricName, Integer isMetricCode, String value, List<Long> metricGroup,
                                              List<SlaObjectTypeEnum> dataSource, List<SlaMetricTypeEnum> slaMetricType, Date startDate, Date endDate, Pageable pageable) {
        value = SqlUtils.optimizeSearchLike(value);

        List<String> dataSources = dataSource.isEmpty()
                ? Collections.singletonList("")
                : dataSource.stream().map(SlaObjectTypeEnum::name).collect(Collectors.toList());
        List<String> metricTypes = slaMetricType.isEmpty()
                ? Collections.singletonList("")
                : slaMetricType.stream().map(SlaMetricTypeEnum::name).collect(Collectors.toList());

        return slaMetricRepository.getListMetric(isMetricName, isMetricCode, value, metricGroup, dataSources,
                metricTypes, startDate, endDate, pageable);
    }

    @Override
    public List<ICommonIdName> getListMetricGroup() {
        return slaMetricRepository.getListMetricGroup();
    }

}
