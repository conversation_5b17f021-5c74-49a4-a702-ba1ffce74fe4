DROP FUNCTION IF EXISTS "vnpt_dev"."func_check_object_match_condition";
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_check_object_match_condition"("objecttype" int4, "objectid" int8, "conditionquery" text)
  RETURNS "pg_catalog"."bool" AS $BODY$
	DECLARE
mQuery text;
		mResult boolean;

BEGIN
		mQuery = 'SELECT EXISTS( ' ;
CASE objectType
			WHEN 0 THEN
				mQuery = CONCAT(mQuery, 'SELECT users.id FROM vnpt_dev.users WHERE users.id = ');
WHEN 1 THEN
				mQuery = CONCAT(mQuery, 'SELECT customer_ticket.id FROM vnpt_dev.customer_ticket ');
				mQuery = CONCAT(mQuery, 'LEFT JOIN vnpt_dev.users ON customer_ticket.user_id = users.id ');
				mQuery = CONCAT(mQuery, 'WHERE customer_ticket.id = ');
WHEN 2 THEN
				mQuery = CONCAT(mQuery, 'SELECT enterprise.id FROM vnpt_dev.enterprise WHERE enterprise.id = ');
WHEN 3 THEN
				mQuery = CONCAT(mQuery, 'SELECT customer_contact.id FROM vnpt_dev.customer_contact WHERE customer_contact.id = ');
WHEN 4 THEN
				mQuery = CONCAT(mQuery, 'SELECT subscriptions.id FROM vnpt_dev.subscriptions ');
				mQuery = CONCAT(mQuery, 'JOIN vnpt_dev.users ON subscriptions.user_id = users.id ');
				mQuery = CONCAT(mQuery, 'WHERE subscriptions.id = ');
WHEN 5 THEN
				mQuery = CONCAT(mQuery, 'SELECT billings.id FROM vnpt_dev.billings ');
				mQuery = CONCAT(mQuery, 'JOIN vnpt_dev.subscriptions ON billings.subscriptions_id = subscriptions.id ');
				mQuery = CONCAT(mQuery, 'JOIN vnpt_dev.users ON subscriptions.user_id = users.id ');
				mQuery = CONCAT(mQuery, 'WHERE billings.id = ');
WHEN 6 THEN
				mQuery = CONCAT(mQuery, 'SELECT e_contract.id FROM vnpt_dev.e_contract ');
				mQuery = CONCAT(mQuery, 'JOIN vnpt_dev.subscriptions ON e_contract.id_subscription = subscriptions.id ');
				mQuery = CONCAT(mQuery, 'JOIN vnpt_dev.users ON users.id = subscriptions.user_id ');
				mQuery = CONCAT(mQuery, 'WHERE e_contract.id = ');
WHEN 8 THEN
mQuery = CONCAT(mQuery, 'SELECT id FROM vnpt_dev.services WHERE id = ');
WHEN 9 THEN
mQuery = CONCAT(mQuery, 'SELECT id FROM vnpt_dev.product_orders WHERE id = ');
ELSE
				RETURN FALSE;
END CASE;

       	mQuery = CONCAT(mQuery, objectId, ' AND ');
		mQuery = CONCAT(mQuery, '(', conditionQuery, '))');
		RAISE NOTICE 'mQuery: %', mQuery;
EXECUTE mQuery INTO mResult;
RETURN mResult;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;