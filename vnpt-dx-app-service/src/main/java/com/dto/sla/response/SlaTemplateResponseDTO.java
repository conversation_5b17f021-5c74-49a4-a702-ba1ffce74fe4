package com.dto.sla.response;

import com.enums.sla.SlaCommitmentTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * DTO cho response SLA Template
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlaTemplateResponseDTO {

    private Long id; // ID của SLA Template
    private String name; // Tên mẫu SLA
    private String code; // Mã mẫu SLA (auto-generated)
    private String description; // Mô tả mục đích, phạm vi áp dụng
    private SlaCommitmentTypeEnum type; // Phân loại SLA
    private StatusEnum status; // Trạng thái

    // Thông tin audit
    private Long createdBy; // ID người tạo
    private Date createdAt; // Thời gian tạo
    private Long modifiedBy; // ID người cập nhật
    private Date modifiedAt; // Thời gian cập nhật

    // Danh sách metrics và alarms
    private List<SlaTemplateMetricResponseDTO> metrics; // Danh sách chỉ số & mục tiêu
    private List<SlaTemplateAlarmResponseDTO> alarms; // Danh sách quy tắc cảnh báo

    /**
     * DTO cho metric response trong SLA Template
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SlaTemplateMetricResponseDTO {
        private Long id;
        private Long metricId;
        private String metricName;
        private String metricLabel;
        private List<SlaMetricThresholdResponseDTO> targetThreshold;
        private List<SlaMetricThresholdResponseDTO> warningThreshold;
        private List<SlaMetricThresholdResponseDTO> breachThreshold;
        private String workingTime;
    }

    /**
     * DTO cho threshold response
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SlaMetricThresholdResponseDTO {
        private String operator;
        private Double value;
        private String unit;
    }

    /**
     * DTO cho alarm response trong SLA Template
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SlaTemplateAlarmResponseDTO {
        private Long id;
        private String name;
        private Long slaTemplateMetricId;
        private String thresholdType;
        private String action;
        private Long notificationId;
        private Long attributeId;
        private Object attributeValue;
        private Long assigneeId;
    }
}
