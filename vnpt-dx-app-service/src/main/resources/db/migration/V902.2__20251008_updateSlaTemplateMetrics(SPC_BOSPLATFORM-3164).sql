-- C<PERSON><PERSON> nhật bảng sla_template_metrics để thêm các cột cần thiết
-- và đảm bảo tương thích với entity mapping

-- Thêm cột sla_template_id và metric_id
ALTER TABLE vnpt_dev.sla_template_metrics 
ADD COLUMN sla_template_id BIGINT,
ADD COLUMN metric_id BIGINT;

-- Tạo index cho performance
CREATE INDEX idx_sla_template_metrics_template_id ON vnpt_dev.sla_template_metrics(sla_template_id);
CREATE INDEX idx_sla_template_metrics_metric_id ON vnpt_dev.sla_template_metrics(metric_id);

-- Thêm comment cho các cột mới
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.sla_template_id IS 'ID mẫu cam kết (ID từ bảng vnpt_dev.sla_templates)';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.metric_id IS 'ID metric (ID từ bảng vnpt_dev.sla_metrics)';

-- <PERSON><PERSON><PERSON> nhật comment cho các cột hiện tại để rõ ràng hơn
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.sla_template_code IS 'Mã mẫu cam kết (code từ bảng vnpt_dev.sla_templates) - deprecated, sử dụng sla_template_id';
COMMENT ON COLUMN vnpt_dev.sla_template_metrics.metric_name IS 'Tên metric (name từ bảng vnpt_dev.sla_metrics) - deprecated, sử dụng metric_id';
