package com.repository.sla;

import com.constant.sql.sla.SQLSlaPolicies;
import com.dto.sla.IGetListPolicies;
import com.entity.sla.SlaPolicy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface SlaPolicyRepository extends JpaRepository<SlaPolicy, Long> {

    boolean existsBySlaTemplateId(Long slaTemplateId);

    @Query(nativeQuery = true, value = SQLSlaPolicies.GET_LIST_POLICIES)
    Page<IGetListPolicies> getListPolicies(Integer isPoliciesName, String value, List<Integer> status,
                                           Long createdBy, Date startDate, Date endDate, Pageable pageable);

    boolean existsByNameAndDeletedFlagAndIdNot(String name, int deletedFlag, Long id);
}
