package com.dto.sla;

import java.util.List;
import com.dto.sla.request.SlaTemplateAlarmCreateDTO;
import com.enums.sla.SlaWorkingTimeEnum;
import com.onedx.common.exception.MessageKeyConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
/**
 * DTO cho cấu hình metric trong SLA Template
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlaTemplateMetricConfigDTO {

    private Long id; // ID của SlaTemplateMetric (null khi tạo mới)

    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private Long metricId; // ID của metric từ thư viện

    private String metricName; // Tên metric (lấy từ thư viện)

    @Valid
    @NotNull(message = "Mục tiêu không được để trống")
    @NotEmpty(message = "Danh sách ngưỡng mục tiêu không được rỗng")
    private List<SlaMetricThresholdDTO> targetThreshold; // Ngưỡng mục tiêu

    @Valid
    private List<SlaMetricThresholdDTO> warningThreshold; // Ngưỡng cảnh báo

    @Valid
    private List<SlaMetricThresholdDTO> breachThreshold; // Ngưỡng vi phạm

    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private SlaWorkingTimeEnum workingTime; // Thời gian làm việc

    @Valid
    private List<SlaTemplateAlarmCreateDTO> alarms; // Danh sách quy tắc cảnh báo cho metric này
}
